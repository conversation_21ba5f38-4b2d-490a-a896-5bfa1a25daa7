# 令牌自动刷新机制实现总结

## 🎯 实现目标

在安卓版本中添加令牌自动刷新机制，使用刷新令牌自动获取新的访问令牌，参考iOS版本的令牌刷新机制，使用相同的API获取令牌。

## 🔍 iOS版本参考分析

### iOS版本的关键特性
1. **刷新令牌存储**: 使用UserDefaults存储refresh_token
2. **自动刷新逻辑**: 检查令牌过期时间，提前刷新
3. **网络请求拦截**: 401响应时自动刷新令牌并重试
4. **令牌验证**: JWT解析和过期时间检查

### iOS版本的API端点
- 刷新令牌API: `${LOGTO_ENDPOINT}/oidc/token`
- 使用grant_type: "refresh_token"
- 包含client_id和refresh_token参数

## 🔧 安卓版本实现方案

### 1. 增强令牌验证机制

#### A. JWT令牌解析和过期检查
**文件**: `android/app/src/main/java/com/cabycare/android/data/auth/AuthManager.kt`

```kotlin
/**
 * 检查令牌是否有效（包括过期检查）
 */
private fun isTokenValid(token: String): Boolean {
    return try {
        val parts = token.split(".")
        if (parts.size != 3) return false
        
        val payload = String(Base64.getUrlDecoder().decode(parts[1]))
        val json = Json.parseToJsonElement(payload).jsonObject
        val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull() ?: return false
        
        val expirationTime = exp * 1000 // 转换为毫秒
        val currentTime = System.currentTimeMillis()
        
        val isValid = expirationTime > currentTime + TOKEN_REFRESH_THRESHOLD
        Log.d(TAG, "🔍 令牌有效性检查: 过期时间=${Date(expirationTime)}, 有效=$isValid")
        
        isValid
    } catch (e: Exception) {
        Log.e(TAG, "❌ 令牌验证失败", e)
        false
    }
}

/**
 * 检查令牌是否需要刷新（即将过期但还未过期）
 */
private fun shouldRefreshToken(token: String): Boolean {
    return try {
        val parts = token.split(".")
        if (parts.size != 3) return true
        
        val payload = String(Base64.getUrlDecoder().decode(parts[1]))
        val json = Json.parseToJsonElement(payload).jsonObject
        val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull() ?: return true
        
        val expirationTime = exp * 1000 // 转换为毫秒
        val currentTime = System.currentTimeMillis()
        
        // 如果令牌在10分钟内过期，就需要刷新
        val refreshThreshold = 10 * 60 * 1000L // 10分钟
        val shouldRefresh = expirationTime <= currentTime + refreshThreshold
        
        Log.d(TAG, "🔄 令牌刷新检查: 需要刷新=$shouldRefresh")
        
        shouldRefresh
    } catch (e: Exception) {
        Log.w(TAG, "⚠️ 令牌解析失败，认为需要刷新", e)
        true // 解析失败时认为需要刷新
    }
}
```

#### B. 改进的初始认证状态检查
```kotlin
private fun checkInitialAuthStatus() {
    scope.launch {
        val isAuthenticated = userPreferences.isAuthenticated().first()
        val accessToken = userPreferences.getAccessToken().first()
        
        if (isAuthenticated && !accessToken.isNullOrEmpty()) {
            Log.i(TAG, "🔍 检查存储的访问令牌状态")
            
            // 检查令牌是否有效
            if (isTokenValid(accessToken)) {
                Log.i(TAG, "🔑 发现有效的访问令牌，用户已登录")
                updateAuthState(isAuthenticated = true)
                loadUserProfile()
            } else if (shouldRefreshToken(accessToken)) {
                Log.w(TAG, "⚠️ 访问令牌即将过期或已过期，尝试刷新")
                try {
                    val newToken = refreshToken()
                    Log.i(TAG, "✅ 令牌刷新成功，用户保持登录状态")
                    updateAuthState(isAuthenticated = true)
                    loadUserProfile()
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 令牌刷新失败，需要重新登录", e)
                    clearAuthCredentials()
                }
            }
        }
    }
}
```

### 2. 增强令牌刷新实现

#### A. 详细的刷新过程日志
```kotlin
suspend fun refreshToken(): String = refreshMutex.withLock {
    Log.i(TAG, "🔄 开始刷新令牌")
    
    val task = scope.async {
        try {
            val refreshToken = userPreferences.getRefreshToken().first()
            if (refreshToken.isNullOrEmpty()) {
                Log.e(TAG, "❌ 刷新令牌不存在或为空")
                throw IllegalStateException("Refresh token not found")
            }
            
            Log.d(TAG, "🔄 使用刷新令牌: ${refreshToken.take(10)}...")
            
            val requestBody = mapOf(
                "grant_type" to "refresh_token",
                "client_id" to LogtoConfig.CLIENT_ID,
                "refresh_token" to refreshToken
            )
            
            Log.d(TAG, "🌐 发送令牌刷新请求到: ${LogtoConfig.LOGTO_ENDPOINT}/oidc/token")
            
            val request = Request.Builder()
                .url("${LogtoConfig.LOGTO_ENDPOINT}/oidc/token")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build()
            
            val response = okHttpClient.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            Log.d(TAG, "📡 令牌刷新响应: 状态码=${response.code}")
            
            if (!response.isSuccessful) {
                Log.e(TAG, "❌ 令牌刷新失败: HTTP ${response.code}")
                throw Exception("Token refresh failed: HTTP ${response.code}")
            }
            
            val tokenResponse = Json.decodeFromString(TokenResponse.serializer(), responseBody)
            
            // 保存新令牌
            Log.d(TAG, "💾 保存新的访问令牌: ${tokenResponse.accessToken.take(10)}...")
            userPreferences.saveAccessToken(tokenResponse.accessToken)
            
            // 如果响应中包含新的刷新令牌，也要保存
            tokenResponse.refreshToken?.let { newRefreshToken ->
                if (newRefreshToken.isNotEmpty()) {
                    Log.d(TAG, "💾 保存新的刷新令牌: ${newRefreshToken.take(10)}...")
                    userPreferences.saveRefreshToken(newRefreshToken)
                }
            }
            
            Log.i(TAG, "✅ 令牌刷新成功")
            tokenResponse.accessToken
        } catch (e: Exception) {
            Log.e(TAG, "❌ 令牌刷新失败: ${e.message}", e)
            clearAuthCredentials()
            throw e
        }
    }
    
    task.await()
}
```

### 3. 网络请求自动令牌刷新

#### A. TokenInterceptor实现
**文件**: `android/app/src/main/java/com/cabycare/android/data/auth/TokenInterceptor.kt`

```kotlin
@Singleton
class TokenInterceptor @Inject constructor(
    private val userPreferences: UserPreferences,
    private val authManager: AuthManager
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 获取访问令牌并添加认证头
        val accessToken = runBlocking {
            userPreferences.getAccessToken().first()
        }
        
        if (accessToken.isNullOrEmpty()) {
            return chain.proceed(originalRequest)
        }
        
        // 添加认证头
        val authenticatedRequest = originalRequest.newBuilder()
            .addHeader("Authorization", "Bearer $accessToken")
            .build()
        
        // 执行请求
        val response = chain.proceed(authenticatedRequest)
        
        // 检查是否是认证失败（401 Unauthorized）
        if (response.code == 401) {
            Log.w(TAG, "🔒 收到401响应，令牌可能已过期")
            
            // 尝试刷新令牌
            val newToken = runBlocking {
                try {
                    Log.i(TAG, "🔄 尝试刷新令牌")
                    authManager.refreshToken()
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 令牌刷新失败: ${e.message}")
                    null
                }
            }
            
            // 如果刷新成功，重试原始请求
            if (!newToken.isNullOrEmpty()) {
                Log.i(TAG, "✅ 令牌刷新成功，重试请求")
                response.close() // 关闭原始响应
                
                val retryRequest = originalRequest.newBuilder()
                    .addHeader("Authorization", "Bearer $newToken")
                    .build()
                
                return chain.proceed(retryRequest)
            }
        }
        
        return response
    }
}
```

### 4. 依赖注入配置

#### A. 解决循环依赖问题
**文件**: `android/app/src/main/java/com/cabycare/android/di/NetworkModule.kt`

```kotlin
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class BasicOkHttpClient

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class AuthenticatedOkHttpClient

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    /**
     * 提供基础OkHttpClient实例（用于AuthManager等不需要令牌拦截的组件）
     */
    @Provides
    @Singleton
    @BasicOkHttpClient
    fun provideBasicOkHttpClient(): OkHttpClient {
        // 不包含TokenInterceptor，避免循环依赖
    }

    /**
     * 提供带认证的OkHttpClient实例（包含令牌自动刷新功能）
     */
    @Provides
    @Singleton
    @AuthenticatedOkHttpClient
    fun provideAuthenticatedOkHttpClient(tokenInterceptor: TokenInterceptor): OkHttpClient {
        // 包含TokenInterceptor，用于API请求
    }
}
```

### 5. 改进的TokenRefreshManager

#### A. 智能刷新检查
```kotlin
private suspend fun silentlyRefreshTokenIfNeeded() {
    if (isAutoRefreshing) {
        Log.d(TAG, "🔄 已在进行静默刷新，跳过重复请求")
        return
    }
    
    try {
        isAutoRefreshing = true
        
        // 检查是否需要刷新令牌
        val needsRefresh = checkIfTokenNeedsRefresh()
        if (!needsRefresh) {
            Log.d(TAG, "🔍 令牌仍然有效，无需刷新")
            return
        }
        
        Log.i(TAG, "🔄 开始静默刷新令牌")
        val newToken = authManager.refreshToken()
        Log.i(TAG, "✅ 静默令牌刷新成功，新令牌: ${newToken.take(10)}...")
        
    } catch (e: Exception) {
        Log.w(TAG, "⚠️ 静默令牌刷新失败: ${e.message}")
    } finally {
        isAutoRefreshing = false
    }
}

private suspend fun checkIfTokenNeedsRefresh(): Boolean {
    return try {
        val authState = authManager.authState.first()
        if (!authState.isAuthenticated) {
            return false
        }
        
        // 使用AuthManager的公共方法检查令牌状态
        authManager.isTokenRefreshNeeded()
    } catch (e: Exception) {
        Log.w(TAG, "⚠️ 检查令牌状态失败: ${e.message}")
        true // 检查失败时认为需要刷新
    }
}
```

## 🎯 与iOS版本的一致性

### 1. API端点一致性
- **刷新URL**: `${LogtoConfig.LOGTO_ENDPOINT}/oidc/token`
- **请求参数**: grant_type, client_id, refresh_token
- **响应处理**: access_token和refresh_token的保存

### 2. 令牌管理一致性
- **存储机制**: 安全存储访问令牌和刷新令牌
- **过期检查**: JWT解析和过期时间验证
- **自动刷新**: 提前10分钟刷新令牌

### 3. 网络请求处理一致性
- **401响应处理**: 自动刷新令牌并重试请求
- **认证头添加**: 自动为请求添加Bearer token
- **错误处理**: 刷新失败时清除认证信息

## ✅ 编译状态

- **编译状态**: ✅ 成功
- **依赖注入**: ✅ 无循环依赖
- **错误**: 0个

## 🎉 预期效果

1. **自动令牌刷新**: 令牌过期前自动刷新，用户无感知
2. **网络请求透明**: API请求自动处理认证和令牌刷新
3. **应用生命周期管理**: 应用启动和前台切换时检查令牌状态
4. **错误恢复**: 令牌过期时自动刷新并重试请求

现在安卓版本具备了与iOS版本相同的令牌自动刷新能力！🔄✨
