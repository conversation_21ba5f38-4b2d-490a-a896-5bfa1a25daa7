package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.Date

/**
 * 用户数据模型
 * 对应Swift版本的User模型
 */
@Serializable
data class User(
    @SerialName("user_id")
    val userId: String,
    val username: String,
    val email: String,
    val phone: String,
    val nickname: String,
    val status: Int,
    @SerialName("created_at")
    val createdAt: String, // 使用String类型，后续转换为Date
    @SerialName("updated_at")
    val updatedAt: String
) {
    /**
     * 用户是否活跃
     */
    val isActive: Boolean
        get() = status == 1
        
    /**
     * 显示名称（优先使用nickname，否则使用username）
     */
    val displayName: String
        get() = nickname.ifEmpty { username }
}

/**
 * OAuth回调响应模型
 * 对应Swift版本的OAuthCallbackResponse
 */
@Serializable
data class OAuthCallbackResponse(
    val status: String,
    val message: String,
    @SerialName("user_id") val userId: String
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 用户信息API响应模型
 * 对应 /api/user/info 的直接响应格式
 */
@Serializable
data class UserInfoResponse(
    val email: String,
    @SerialName("logto_id") val logtoId: String,
    @SerialName("user_id") val userId: String,
    val username: String,
    val message: String,
    val nickname: String? = null,
    @SerialName("logto_user_info") val logtoUserInfo: LogtoUserInfo? = null
)

/**
 * Logto用户信息
 */
@Serializable
data class LogtoUserInfo(
    val sub: String,
    val name: String? = null,
    val email: String? = null,
    val picture: String? = null,
    @SerialName("email_verified") val emailVerified: Boolean? = null,
    val locale: String? = null,
    @SerialName("updated_at") val updatedAt: Long? = null
)


