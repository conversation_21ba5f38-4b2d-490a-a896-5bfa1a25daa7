package com.cabycare.android.ui

import android.util.Log
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.ui.auth.AuthViewModel
import com.cabycare.android.ui.auth.LoginScreen
import com.cabycare.android.ui.main.MainScreen
import com.cabycare.android.ui.splash.SplashScreen
import kotlinx.coroutines.delay

/**
 * CabyCare应用程序的根Composable
 * 负责管理应用程序的整体导航和状态
 */
@Composable
fun CabyCareApp(
    modifier: Modifier = Modifier,
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val authState by authViewModel.authState.collectAsState()
    var isLaunching by remember { mutableStateOf(true) }
    
    // 启动时立即检查令牌状态
    LaunchedEffect(authState.isAuthenticated, authState.isLoading) {
        if (!authState.isLoading) {
            if (authState.isAuthenticated) {
                // 如果已经认证，立即跳过启动画面
                Log.i("CabyCareApp", "🔑 令牌有效，直接进入主界面")
                isLaunching = false
            } else {
                // 如果未认证，显示启动画面1秒后进入登录界面
                delay(1000)
                isLaunching = false
            }
        }
    }

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when {
            isLaunching -> {
                SplashScreen()
            }
            authState.isLoading -> {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(16.dp)
                )
            }
            authState.isAuthenticated -> {
                MainScreen()
            }
            else -> {
                LoginScreen(
                    onLoginSuccess = {
                        authViewModel.refreshAuthState()
                    }
                )
            }
        }
    }
}
