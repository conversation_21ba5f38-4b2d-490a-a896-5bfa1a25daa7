package com.cabycare.android.ui.video

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.LoadControl
import androidx.media3.exoplayer.upstream.DefaultAllocator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 自适应缓冲管理器
 * 根据网络状况和设备性能动态调整缓冲策略
 */
class AdaptiveBufferManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AdaptiveBufferManager"
        
        // 网络类型枚举
        enum class NetworkType {
            WIFI,
            CELLULAR_5G,
            CELLULAR_4G,
            CELLULAR_3G,
            CELLULAR_2G,
            UNKNOWN
        }
        
        // 缓冲策略配置
        data class BufferConfig(
            val minBufferMs: Int,
            val maxBufferMs: Int,
            val bufferForPlaybackMs: Int,
            val bufferForPlaybackAfterRebufferMs: Int,
            val targetBufferBytes: Int,
            val allocatorBlockSize: Int
        )
    }
    
    private val _currentNetworkType = MutableStateFlow(NetworkType.UNKNOWN)
    val currentNetworkType: StateFlow<NetworkType> = _currentNetworkType.asStateFlow()
    
    private val _currentBufferConfig = MutableStateFlow(getDefaultBufferConfig())
    val currentBufferConfig: StateFlow<BufferConfig> = _currentBufferConfig.asStateFlow()
    
    /**
     * 获取当前网络类型
     */
    fun getCurrentNetworkType(): NetworkType {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return NetworkType.UNKNOWN
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.UNKNOWN
        
        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                // 尝试检测蜂窝网络类型
                when {
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) -> {
                        // 简化的网络类型检测，实际应用中可以更精确
                        NetworkType.CELLULAR_4G
                    }
                    else -> NetworkType.CELLULAR_3G
                }
            }
            else -> NetworkType.UNKNOWN
        }
    }
    
    /**
     * 更新网络状态并调整缓冲策略
     */
    fun updateNetworkStatus() {
        val newNetworkType = getCurrentNetworkType()
        if (newNetworkType != _currentNetworkType.value) {
            _currentNetworkType.value = newNetworkType
            _currentBufferConfig.value = getBufferConfigForNetwork(newNetworkType)
            Log.d(TAG, "网络类型变更: $newNetworkType, 更新缓冲策略: ${_currentBufferConfig.value}")
        }
    }
    
    /**
     * 根据网络类型获取缓冲配置
     */
    private fun getBufferConfigForNetwork(networkType: NetworkType): BufferConfig {
        return when (networkType) {
            NetworkType.WIFI -> BufferConfig(
                minBufferMs = 8000,           // WiFi下可以更激进的缓冲
                maxBufferMs = 40000,          // 更大的最大缓冲
                bufferForPlaybackMs = 2000,   // 快速启动
                bufferForPlaybackAfterRebufferMs = 4000,
                targetBufferBytes = 30 * 1024 * 1024, // 30MB
                allocatorBlockSize = 32 * 1024 // 32KB块
            )
            
            NetworkType.CELLULAR_5G -> BufferConfig(
                minBufferMs = 6000,
                maxBufferMs = 30000,
                bufferForPlaybackMs = 2500,
                bufferForPlaybackAfterRebufferMs = 5000,
                targetBufferBytes = 25 * 1024 * 1024, // 25MB
                allocatorBlockSize = 24 * 1024 // 24KB块
            )
            
            NetworkType.CELLULAR_4G -> BufferConfig(
                minBufferMs = 5000,           // 默认配置
                maxBufferMs = 25000,
                bufferForPlaybackMs = 2500,
                bufferForPlaybackAfterRebufferMs = 5000,
                targetBufferBytes = 20 * 1024 * 1024, // 20MB
                allocatorBlockSize = 16 * 1024 // 16KB块
            )
            
            NetworkType.CELLULAR_3G -> BufferConfig(
                minBufferMs = 8000,           // 3G网络需要更多缓冲
                maxBufferMs = 20000,
                bufferForPlaybackMs = 4000,   // 更保守的启动
                bufferForPlaybackAfterRebufferMs = 8000,
                targetBufferBytes = 15 * 1024 * 1024, // 15MB
                allocatorBlockSize = 12 * 1024 // 12KB块
            )
            
            NetworkType.CELLULAR_2G -> BufferConfig(
                minBufferMs = 12000,          // 2G网络需要大量缓冲
                maxBufferMs = 15000,
                bufferForPlaybackMs = 6000,
                bufferForPlaybackAfterRebufferMs = 10000,
                targetBufferBytes = 10 * 1024 * 1024, // 10MB
                allocatorBlockSize = 8 * 1024 // 8KB块
            )
            
            NetworkType.UNKNOWN -> getDefaultBufferConfig()
        }
    }
    
    /**
     * 获取默认缓冲配置
     */
    private fun getDefaultBufferConfig(): BufferConfig {
        return BufferConfig(
            minBufferMs = 5000,
            maxBufferMs = 25000,
            bufferForPlaybackMs = 2500,
            bufferForPlaybackAfterRebufferMs = 5000,
            targetBufferBytes = 20 * 1024 * 1024,
            allocatorBlockSize = 16 * 1024
        )
    }
    
    /**
     * 创建自适应LoadControl
     */
    fun createAdaptiveLoadControl(): LoadControl {
        val config = _currentBufferConfig.value
        
        return DefaultLoadControl.Builder()
            .setAllocator(DefaultAllocator(true, config.allocatorBlockSize))
            .setBufferDurationsMs(
                config.minBufferMs,
                config.maxBufferMs,
                config.bufferForPlaybackMs,
                config.bufferForPlaybackAfterRebufferMs
            )
            .setTargetBufferBytes(config.targetBufferBytes)
            .setPrioritizeTimeOverSizeThresholds(false)
            .setBackBuffer(config.minBufferMs / 2, true) // 后台缓冲为最小缓冲的一半
            .build().also {
                Log.d(TAG, "创建自适应LoadControl: $config")
            }
    }
    
    /**
     * 根据缓冲状态动态调整策略
     */
    fun adjustBufferStrategy(
        bufferPercentage: Int,
        bufferAheadMs: Long,
        consecutiveLowBufferCount: Int
    ) {
        // 如果连续出现低缓冲，临时降低缓冲要求
        if (consecutiveLowBufferCount >= 3) {
            val currentConfig = _currentBufferConfig.value
            val adjustedConfig = currentConfig.copy(
                minBufferMs = maxOf(2000, currentConfig.minBufferMs - 1000),
                bufferForPlaybackMs = maxOf(1000, currentConfig.bufferForPlaybackMs - 500)
            )
            _currentBufferConfig.value = adjustedConfig
            Log.d(TAG, "检测到持续缓冲问题，临时调整策略: $adjustedConfig")
        }
    }
}
