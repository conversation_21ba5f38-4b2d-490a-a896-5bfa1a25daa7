package com.cabycare.android.ui.care

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.model.AccessibleDevice
import com.cabycare.android.data.model.VideoSegmentRecord
import com.cabycare.android.data.model.VideoListResponse
import com.cabycare.android.data.model.DailyVideoStats
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.local.UserPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.serialization.SerializationException
import java.util.*
import java.text.SimpleDateFormat
import android.util.Log
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import javax.inject.Inject

/**
 * 视频ViewModel - 基于iOS VideoPlayerViewModel设计
 * 负责：
 * 1. 获取用户所有设备 (对应iOS DeviceManager.loadDevices)
 * 2. 获取设备视频数据并缓存 (对应iOS DeviceManager.loadSegments)
 * 3. 管理日期选择和视频过滤 (对应iOS VideoPlayerViewModel日期管理)
 * 4. 控制视频播放状态
 */
@HiltViewModel
class VideoViewModel @Inject constructor(
    private val apiService: ApiService,
    private val userPreferences: UserPreferences
) : ViewModel() {

    private val _uiState = MutableStateFlow(VideoUiState())
    val uiState: StateFlow<VideoUiState> = _uiState.asStateFlow()
    
    // 缓存所有视频数据 - 按设备ID存储 (对应iOS DeviceManager.deviceSegments)
    private val videoCache = mutableMapOf<String, List<VideoSegmentRecord>>()
    
    // 已缓存的日期范围 - 避免重复请求历史数据 (对应iOS DeviceManager.cachedDateRanges)
    private val cachedDateRanges = mutableMapOf<String, MutableList<ClosedRange<Date>>>()
    
    // 设备可用日期缓存 (对应iOS DeviceManager.deviceDates)
    private val deviceDateCache = mutableMapOf<String, List<Date>>()
    
    // 最后完整加载时间 (对应iOS DeviceManager.lastFullLoadTime)
    private val lastFullLoadTime = mutableMapOf<String, Date>()
    
    // 缓存过期时间 (5分钟，与iOS一致)
    private val cacheExpirationInterval = 5 * 60 * 1000L // 5分钟
    
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val isoFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault()).apply {
        timeZone = TimeZone.getTimeZone("UTC")
    }
    
    companion object {
        private const val TAG = "VideoViewModel"
    }
    
    init {
        // 自动加载数据
        loadInitialData()
    }
    
    /**
     * 获取访问令牌（用于视频播放认证）
     */
    suspend fun getAccessToken(): String? {
        return try {
            userPreferences.getAccessToken().first()
        } catch (e: Exception) {
            Log.e(TAG, "获取访问令牌失败", e)
            null
        }
    }
    
    /**
     * 加载初始数据 - 对应iOS VideoPlayerViewModel的ensureInitialDataLoaded
     */
    private fun loadInitialData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // 1. 获取用户设备
                val devices = fetchUserDevices()
                _uiState.value = _uiState.value.copy(devices = devices)
                
                // 2. 如果有设备，加载可用日期列表（最近一年）
                if (devices.isNotEmpty()) {
                    loadAvailableDates(devices)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "加载初始数据失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "加载数据失败: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    /**
     * 获取用户设备列表 - 对应iOS DeviceManager.loadDevices
     */
    private suspend fun fetchUserDevices(): List<AccessibleDevice> {
        return try {
            val userId = userPreferences.getUserId().first()
            if (userId.isNullOrEmpty()) {
                Log.w(TAG, "用户ID为空，无法获取设备列表")
                throw Exception("用户ID无效")
            }
            
            Log.d(TAG, "开始获取用户设备列表: userId=$userId")
            
            val response = apiService.getAccessibleDevices(userId)
            Log.d(TAG, "获取设备列表成功: 状态=${response.status}, 消息=${response.message}, 设备数量=${response.data.size}")
            
            if (response.data.isEmpty()) {
                Log.i(TAG, "用户 $userId 目前没有绑定任何设备")
            } else {
                // 详细打印每个设备信息
                response.data.forEach { device ->
                    Log.d(TAG, "设备详情: ID=${device.deviceId}, 名称=${device.name}, 型号=${device.model}, 状态=${device.status}")
                }
            }
            
            response.data
        } catch (e: Exception) {
            Log.e(TAG, "获取设备列表失败", e)
            throw Exception("获取设备列表失败: ${e.message}")
        }
    }
    
    /**
     * 加载可用日期列表 - 对应iOS VideoPlayerViewModel.loadAvailableDates
     */
    private suspend fun loadAvailableDates(devices: List<AccessibleDevice>) {
        val calendar = Calendar.getInstance()
        val now = Date()
        
        // 设置日期范围为过去一年到明天
        val endDate = calendar.apply {
            time = now
            add(Calendar.DAY_OF_MONTH, 1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.time
        
        val startDate = calendar.apply {
            time = now
            add(Calendar.YEAR, -1)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.time
        
        val allAvailableDates = mutableSetOf<Date>()
        val allSegments = mutableListOf<VideoSegmentRecord>()
        
        // 遍历所有设备加载视频段
        for (device in devices) {
            try {
                Log.d(TAG, "正在加载设备 ${device.name} (${device.deviceId}) 的视频数据")
                
                // 加载设备的所有视频段
                val segments = loadDeviceSegments(device, startDate, endDate)
                
                // 缓存设备的视频段
                videoCache[device.deviceId] = segments
                allSegments.addAll(segments)
                
                // 计算该设备的可用日期
                val deviceDates = segments.map { segment ->
                    val startOfDay = calendar.apply {
                        time = parseStartTime(segment.start)
                        set(Calendar.HOUR_OF_DAY, 0)
                        set(Calendar.MINUTE, 0)
                        set(Calendar.SECOND, 0)
                        set(Calendar.MILLISECOND, 0)
                    }.time
                    startOfDay
                }.toSet()
                
                deviceDateCache[device.deviceId] = deviceDates.sortedDescending()
                allAvailableDates.addAll(deviceDates)
                
                Log.d(TAG, "设备 ${device.name} 有 ${deviceDates.size} 个可用日期，${segments.size} 个视频片段")
                
            } catch (e: Exception) {
                Log.e(TAG, "设备 ${device.name} 加载视频失败", e)
                // 继续处理其他设备，不中断整个流程
                continue
            }
        }
        
        // 更新可用日期（按日期降序排列）
        val sortedDates = allAvailableDates.sortedDescending()
        
        _uiState.value = _uiState.value.copy(
            availableDates = sortedDates,
            selectedDate = sortedDates.firstOrNull(),
            isLoading = false
        )
        
        Log.d(TAG, "总共收集到 ${sortedDates.size} 个可用日期，${allSegments.size} 个视频片段")
        
        // 如果有日期，选择最新日期的视频
        sortedDates.firstOrNull()?.let { latestDate ->
            selectDate(latestDate)
        }
    }
    
    /**
     * 解析视频开始时间
     */
    private fun parseStartTime(startTimestamp: Long): Date {
        return Date(startTimestamp * 1000) // Unix时间戳转换为毫秒
    }
    
    /**
     * 根据API响应加载指定设备的视频片段
     * 修复：添加SerializationException处理
     */
    private suspend fun loadDeviceSegments(
        device: AccessibleDevice,
        startDate: Date,
        endDate: Date
    ): List<VideoSegmentRecord> {
        val deviceId = device.deviceId
        
        // 检查缓存是否过期
        val lastLoadTime = lastFullLoadTime[deviceId]
        if (lastLoadTime != null && Date().time - lastLoadTime.time < cacheExpirationInterval) {
            // 缓存未过期，检查是否已有该日期范围的数据
            if (hasDataInCache(deviceId, startDate, endDate)) {
                Log.d(TAG, "设备 ${device.name} 使用缓存数据")
                return getCachedSegmentsInRange(deviceId, startDate, endDate)
            }
        }
        
        // 验证日期范围
        val today = Date()
        val calendar = Calendar.getInstance()
        calendar.time = today
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        val tomorrow = calendar.time
        
        val validatedStartDate = if (startDate > today) today else startDate
        val validatedEndDate = if (endDate > tomorrow) tomorrow else endDate
        
        val startDateStr = dateFormatter.format(validatedStartDate)
        val endDateStr = dateFormatter.format(validatedEndDate)
        
        Log.d(TAG, "开始加载设备 ${device.name} 的视频数据，日期范围: $startDateStr 到 $endDateStr")
        
        return try {
            // 使用与iOS一致的API格式: /api/records/videos/list?path=device{deviceId}&start={start}&end={end}
            val devicePath = "device${device.deviceId}"
            val response = apiService.getVideoList(
                path = devicePath,
                start = startDateStr,
                end = endDateStr
            )
            
            // 处理null响应：如果API返回null（设备没有视频），转换为空列表
            val rawVideoSegments = response ?: emptyList<VideoSegmentRecord>()

            // 为每个视频片段设置设备名称
            val videoSegments = rawVideoSegments.map { segment ->
                segment.copy(deviceName = device.name)
            }

            Log.d(TAG, "设备 ${device.name} 在日期范围 $startDateStr 到 $endDateStr 获得 ${videoSegments.size} 个视频片段")
            
            // 记录缓存的日期范围
            val cachedRange = validatedStartDate..validatedEndDate
            cachedDateRanges.getOrPut(deviceId) { mutableListOf() }.add(cachedRange)
            
            // 优化缓存范围
            optimizeCachedRanges(deviceId)
            
            // 更新加载时间
            lastFullLoadTime[deviceId] = Date()
            
            videoSegments
            
        } catch (e: SerializationException) {
            // 捕获JSON解析异常，这通常发生在服务器返回"null"字符串时
            Log.d(TAG, "🔄 设备 ${device.name} 返回无效JSON响应（可能是null），使用空列表: ${e.message}")
            emptyList<VideoSegmentRecord>()
        } catch (e: retrofit2.HttpException) {
            // 如果是404或其他HTTP错误，也返回空列表
            if (e.code() == 404) {
                Log.d(TAG, "🔄 设备 ${device.name} 未找到或无数据，返回空列表")
                emptyList<VideoSegmentRecord>()
            } else {
                Log.e(TAG, "❌ 设备 ${device.name} 发生HTTP错误: ${e.code()}", e)
                emptyList<VideoSegmentRecord>()
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 设备 ${device.name} 加载视频失败", e)
            // 如果是其他网络错误，返回空列表而不是抛出异常
            emptyList<VideoSegmentRecord>()
        }
    }
    
    /**
     * 选择日期 - 对应iOS VideoPlayerViewModel的selectedDate setter
     */
    fun selectDate(date: Date) {
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val dayStart = calendar.time
        
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        val dayEnd = calendar.time
        
        Log.d(TAG, "选择日期: ${dateFormatter.format(date)}")
        
        // 智能加载该日期的视频数据
        viewModelScope.launch {
            loadVideoDataForDate(date, dayStart, dayEnd)
        }
    }
    
    /**
     * 智能加载指定日期的视频数据 - 对应iOS VideoPlayerViewModel.loadVideoDataForDate
     */
    private suspend fun loadVideoDataForDate(date: Date, dayStart: Date, dayEnd: Date) {
        val devices = _uiState.value.devices
        
        if (devices.isEmpty()) {
            Log.w(TAG, "设备列表为空，无法加载视频数据")
            return
        }
        
        val allDayVideos = mutableListOf<VideoSegmentRecord>()
        val devicesMissingData = mutableListOf<AccessibleDevice>()
        
        // 检查每个设备是否有该日期的缓存数据
        for (device in devices) {
            val cachedVideos = getCachedSegmentsForDate(device.deviceId, dayStart, dayEnd)
            
            if (cachedVideos.isNotEmpty()) {
                allDayVideos.addAll(cachedVideos)
                Log.d(TAG, "设备 ${device.name} 有 ${cachedVideos.size} 个缓存视频")
            } else {
                // 检查该日期是否在可用日期列表中
                val deviceAvailableDates = deviceDateCache[device.deviceId] ?: emptyList()
                val hasDataForDate = deviceAvailableDates.any { availableDate ->
                    dateFormatter.format(availableDate) == dateFormatter.format(date)
                }
                
                if (hasDataForDate || isRecentDate(date)) {
                    // 有数据但未缓存，或者是近期日期，需要加载
                    devicesMissingData.add(device)
                    Log.d(TAG, "设备 ${device.name} 在日期 ${dateFormatter.format(date)} 需要加载数据")
                } else {
                    Log.d(TAG, "设备 ${device.name} 在日期 ${dateFormatter.format(date)} 确认没有数据")
                }
            }
        }
        
        // 如果有设备缺少数据，为这些设备发起API请求
        if (devicesMissingData.isNotEmpty()) {
            Log.d(TAG, "需要为 ${devicesMissingData.size} 个设备加载数据")
            
            for (device in devicesMissingData) {
                try {
                    val newSegments = loadDeviceSegments(device, dayStart, dayEnd)
                    
                    // 筛选出该日期的数据
                    val daySegments = newSegments.filter { segment ->
                        val segmentTime = parseStartTime(segment.start)
                        segmentTime.after(dayStart) && segmentTime.before(dayEnd)
                    }
                    
                    allDayVideos.addAll(daySegments)
                    
                    // 更新设备的缓存
                    val existingSegments = videoCache[device.deviceId] ?: emptyList()
                    val existingIds = existingSegments.map { it.id }.toSet()
                    val newUniqueSegments = newSegments.filter { it.id !in existingIds }
                    
                    videoCache[device.deviceId] = (existingSegments + newUniqueSegments)
                        .sortedByDescending { parseStartTime(it.start) }
                    
                    Log.d(TAG, "设备 ${device.name} 新加载 ${daySegments.size} 个当日视频")
                    
                } catch (e: Exception) {
                    Log.e(TAG, "设备 ${device.name} 加载日期 ${dateFormatter.format(date)} 的视频失败", e)
                    continue
                }
            }
        }
        
        // 按时间排序并更新UI状态
        val sortedVideos = allDayVideos.sortedByDescending { parseStartTime(it.start) }
        
        _uiState.value = _uiState.value.copy(
            selectedDate = date,
            videoSegments = sortedVideos,
            dailyStats = calculateDailyStats(date, sortedVideos)
        )
        
        Log.d(TAG, "日期 ${dateFormatter.format(date)} 共找到 ${sortedVideos.size} 个视频片段")
    }
    
    // MARK: - 缓存管理方法
    
    /**
     * 检查指定日期范围的数据是否已在缓存中
     */
    private fun hasDataInCache(deviceId: String, startDate: Date, endDate: Date): Boolean {
        val ranges = cachedDateRanges[deviceId] ?: return false
        return ranges.any { range ->
            range.contains(startDate) && range.contains(endDate)
        }
    }
    
    /**
     * 获取缓存中指定日期范围的视频段
     */
    private fun getCachedSegmentsInRange(deviceId: String, startDate: Date, endDate: Date): List<VideoSegmentRecord> {
        val allSegments = videoCache[deviceId] ?: return emptyList()
        return allSegments.filter { segment ->
            val segmentTime = parseStartTime(segment.start)
            segmentTime.after(startDate) && segmentTime.before(endDate)
        }
    }
    
    /**
     * 获取缓存中指定日期的视频段
     */
    private fun getCachedSegmentsForDate(deviceId: String, dayStart: Date, dayEnd: Date): List<VideoSegmentRecord> {
        return getCachedSegmentsInRange(deviceId, dayStart, dayEnd)
    }
    
    /**
     * 检查是否为近期日期（一周内）
     */
    private fun isRecentDate(date: Date): Boolean {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_MONTH, -7) // 一周前
        val weekAgo = calendar.time
        return date.after(weekAgo)
    }
    
    /**
     * 优化缓存：合并重叠的日期范围
     */
    private fun optimizeCachedRanges(deviceId: String) {
        val ranges = cachedDateRanges[deviceId] ?: return
        if (ranges.size <= 1) return
        
        // 按开始日期排序
        val sortedRanges = ranges.sortedBy { it.start }
        val optimizedRanges = mutableListOf<ClosedRange<Date>>()
        
        var currentRange = sortedRanges[0]
        
        for (i in 1 until sortedRanges.size) {
            val nextRange = sortedRanges[i]
            
            // 检查是否可以合并
            if (currentRange.endInclusive >= nextRange.start) {
                // 合并范围
                val newEnd = maxOf(currentRange.endInclusive, nextRange.endInclusive)
                currentRange = currentRange.start..newEnd
            } else {
                optimizedRanges.add(currentRange)
                currentRange = nextRange
            }
        }
        
        optimizedRanges.add(currentRange)
        cachedDateRanges[deviceId] = optimizedRanges
        
        Log.d(TAG, "设备 $deviceId 缓存范围优化完成，从 ${ranges.size} 个范围合并为 ${optimizedRanges.size} 个")
    }
    
    /**
     * 计算每日统计数据
     */
    private fun calculateDailyStats(date: Date, videos: List<VideoSegmentRecord>): DailyVideoStats {
        val totalDuration = videos.sumOf { it.getDurationSeconds() }
        return DailyVideoStats(
            date = date,
            videoCount = videos.size,
            totalDuration = totalDuration,
            segments = videos // 直接使用VideoSegmentRecord列表
        )
    }
    
    // MARK: - 视频播放控制

    /**
     * 切换视频播放状态
     * 修复：使用正确的API获取m3u8播放列表
     */
    fun togglePlayback(segment: VideoSegmentRecord) {
        val currentState = _uiState.value
        
        if (currentState.currentPlayingVideo?.id == segment.id && currentState.isVideoPlaying) {
            // 如果当前正在播放这个视频，则暂停
            _uiState.value = currentState.copy(isVideoPlaying = false)
            Log.d(TAG, "暂停播放视频: ${segment.id}")
        } else {
            // 播放新视频或恢复播放
            Log.d(TAG, "准备播放视频: ${segment.id}")
            
            // 构建完整的播放URL（直接给ExoPlayer使用，不需要预先获取m3u8内容）
            viewModelScope.launch {
                try {
                    // 从VideoSegmentRecord中提取设备ID
                    val deviceId = extractDeviceIdFromSegment(segment)
                    val devicePath = "device$deviceId"
                    
                    // 将Unix时间戳转换为ISO 8601格式
                    val isoStartTime = convertUnixToIso8601(segment.start)
                    
                    // 构建完整的播放URL - ExoPlayer可以直接使用这个URL
                    val playbackUrl = buildPlaybackUrl(devicePath, isoStartTime, segment.duration)
                    
                    Log.d(TAG, "构建播放URL: $playbackUrl")
                    
                    // 更新状态：设置当前播放的视频和播放URL
                    // ExoPlayer会自动处理HLS播放列表的下载和解析
                    _uiState.value = currentState.copy(
                        currentPlayingVideo = segment.copy(url = playbackUrl), // 使用新的播放URL
                        isVideoPlaying = true,
                        error = null
                    )
                    
                    Log.d(TAG, "开始播放视频: ${segment.id}, URL: $playbackUrl")
                    
                } catch (e: Exception) {
                    Log.e(TAG, "构建播放URL失败: ${e.message}", e)
                    _uiState.value = currentState.copy(
                        error = "视频播放失败：${e.message}"
                    )
                }
            }
        }
    }
    
    /**
     * 从VideoSegmentRecord中提取设备ID
     */
    private fun extractDeviceIdFromSegment(segment: VideoSegmentRecord): String {
        // 首先尝试使用内置的deviceId属性
        val deviceId = segment.deviceId
        if (deviceId != "unknown") {
            return deviceId
        }
        
        // 如果deviceId不可用，尝试从当前设备列表中推断
        val currentDevices = _uiState.value.devices
        if (currentDevices.isNotEmpty()) {
            // 使用第一个设备（假设当前只显示一个设备的视频）
            return currentDevices.first().deviceId
        }
        
        // 作为后备方案，返回一个默认值
        Log.w(TAG, "无法提取设备ID，使用默认值")
        return "unknown"
    }
    
    /**
     * 将Unix时间戳转换为ISO 8601格式（带时区偏移）
     * 修复：使用带时区偏移的格式，符合API文档推荐格式
     */
    private fun convertUnixToIso8601(unixTimestamp: Long): String {
        val date = Date(unixTimestamp * 1000) // 转换为毫秒
        
        // 使用带时区偏移的格式：2025-07-11T10:57:34+08:00
        // 这是API文档中推荐的格式，也是测试成功的格式
        val formatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.getDefault())
        
        // 使用系统默认时区，这样会自动添加正确的时区偏移
        // 例如在中国会输出 +08:00，在UTC会输出 +00:00
        formatter.timeZone = TimeZone.getDefault()
        
        val result = formatter.format(date)
        Log.d(TAG, "时间转换: Unix $unixTimestamp -> ISO $result")
        return result
    }
    
    /**
     * 构建完整的播放URL
     */
    private fun buildPlaybackUrl(path: String, start: String, duration: String): String {
        // 构建完整的API URL用于播放器
        val baseUrl = "https://api.caby.care" // 与NetworkManager中的BASE_URL保持一致
        
        // 构建完整的path：records/device123
        val fullPath = "records/$path"
        
        // 对所有URL参数进行编码
        val encodedPath = URLEncoder.encode(fullPath, StandardCharsets.UTF_8.toString())
        val encodedStart = URLEncoder.encode(start, StandardCharsets.UTF_8.toString())
        val encodedDuration = URLEncoder.encode(duration, StandardCharsets.UTF_8.toString())
        
        val finalUrl = "$baseUrl/api/records/videos/get?path=$encodedPath&start=$encodedStart&duration=$encodedDuration"
        
        Log.d(TAG, "构建播放URL完成:")
        Log.d(TAG, "  原始参数: path=records/$path, start=$start, duration=$duration")
        Log.d(TAG, "  最终URL: $finalUrl")
        
        return finalUrl
    }

    /**
     * 停止播放
     */
    fun stopPlayback() {
        _uiState.value = _uiState.value.copy(
            currentPlayingVideo = null,
            isVideoPlaying = false
        )
        Log.d(TAG, "停止播放视频")
    }
    
    // MARK: - 数据刷新
    
    /**
     * 刷新所有数据
     */
    fun refreshAllData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isRefreshing = true)
            
            try {
                // 清除所有缓存
                clearAllCache()
                
                // 重新加载数据
                loadInitialData()
                
            } catch (e: Exception) {
                Log.e(TAG, "刷新数据失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "刷新失败: ${e.message}"
                )
            } finally {
                _uiState.value = _uiState.value.copy(isRefreshing = false)
            }
        }
    }
    
    /**
     * 清除所有缓存
     */
    private fun clearAllCache() {
        videoCache.clear()
        cachedDateRanges.clear()
        deviceDateCache.clear()
        lastFullLoadTime.clear()
        Log.d(TAG, "所有缓存已清除")
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 视频UI状态数据类
 */
data class VideoUiState(
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val error: String? = null,
    val devices: List<AccessibleDevice> = emptyList(),
    val availableDates: List<Date> = emptyList(),
    val selectedDate: Date? = null,
    val videoSegments: List<VideoSegmentRecord> = emptyList(),
    val currentPlayingVideo: VideoSegmentRecord? = null,
    val isVideoPlaying: Boolean = false,
    val dailyStats: DailyVideoStats? = null
) 