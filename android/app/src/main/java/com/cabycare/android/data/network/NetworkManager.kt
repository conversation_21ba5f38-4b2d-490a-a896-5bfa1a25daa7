package com.cabycare.android.data.network

import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import kotlinx.coroutines.flow.first
import kotlinx.serialization.json.Json
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络管理器
 * 负责配置Retrofit、OkHttp和网络拦截器
 */
@Singleton
class NetworkManager @Inject constructor(
    private val userPreferences: UserPreferences
) {
    companion object {
        private const val TAG = "NetworkManager"
        private const val BASE_URL = "https://api.caby.care" // 替换为实际的API地址
        private const val CONNECT_TIMEOUT = 30L
        private const val READ_TIMEOUT = 30L
        private const val WRITE_TIMEOUT = 30L
    }
    
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
        encodeDefaults = true
    }
    
    /**
     * 认证拦截器
     * 自动在请求头中添加Authorization token
     */
    private inner class AuthInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            
            // 获取访问令牌
            val accessToken = runCatching {
                kotlinx.coroutines.runBlocking {
                    userPreferences.getAccessToken().first()
                }
            }.getOrNull()
            
            val newRequest = if (!accessToken.isNullOrEmpty()) {
                originalRequest.newBuilder()
                    .addHeader("Authorization", "Bearer $accessToken")
                    .build()
            } else {
                originalRequest
            }
            
            return chain.proceed(newRequest)
        }
    }
    
    /**
     * 错误处理拦截器
     * 处理通用的HTTP错误和认证过期
     */
    private inner class ErrorHandlingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val response = chain.proceed(request)
            
            when (response.code) {
                401 -> {
                    Log.w(TAG, "认证失败，令牌可能已过期")
                    // 这里可以触发令牌刷新逻辑
                }
                403 -> {
                    Log.w(TAG, "权限不足")
                }
                404 -> {
                    Log.w(TAG, "请求的资源不存在: ${request.url}")
                }
                500, 502, 503, 504 -> {
                    Log.e(TAG, "服务器错误: ${response.code}")
                }
            }
            
            return response
        }
    }
    
    /**
     * 创建OkHttp客户端
     */
    private fun createOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            Log.d(TAG, message)
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        return OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(AuthInterceptor())
            .addInterceptor(ErrorHandlingInterceptor())
            .addInterceptor(loggingInterceptor)
            .build()
    }
    
    /**
     * 创建Retrofit实例
     */
    fun createRetrofit(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(createOkHttpClient())
            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .build()
    }
    
    /**
     * 创建API服务
     */
    inline fun <reified T> createApiService(): T {
        return createRetrofit().create(T::class.java)
    }
}

/**
 * 网络结果封装类
 */
sealed class NetworkResult<out T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error(val exception: Throwable) : NetworkResult<Nothing>()
    data class Loading(val isLoading: Boolean = true) : NetworkResult<Nothing>()
}

/**
 * 网络请求扩展函数
 * 统一处理网络请求的异常和结果
 */
suspend fun <T> safeApiCall(
    apiCall: suspend () -> T
): NetworkResult<T> {
    return try {
        NetworkResult.Success(apiCall())
    } catch (e: Exception) {
        Log.e("NetworkManager", "API调用失败", e)
        NetworkResult.Error(e)
    }
}
