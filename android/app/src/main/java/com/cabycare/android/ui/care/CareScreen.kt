package com.cabycare.android.ui.care

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.data.model.VideoSegmentRecord
import com.cabycare.android.data.model.DailyVideoStats
import com.cabycare.android.data.model.DeviceStatusInfo
import com.cabycare.android.ui.video.HLSVideoPlayer
import java.text.SimpleDateFormat
import java.util.*

/**
 * 关爱页面 - 基于iOS CareView和VideoListView设计
 * 主要功能：
 * 1. 获取当前用户所有设备
 * 2. 获取所有设备的所有视频，缓存视频信息
 * 3. 日期选择器，选择对应日期显示对应视频
 * 4. HLS视频播放器，点击视频即可播放
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CareScreen(
    viewModel: VideoViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(Unit) {
        // 页面加载时不需要手动调用，VideoViewModel.init()已自动加载
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 顶部标题栏
        CareTopBar(
            onRefresh = { viewModel.refreshAllData() },
            isRefreshing = uiState.isRefreshing
        )
        
        when {
            uiState.isLoading -> {
                // 加载状态
                LoadingView()
            }
            
            uiState.error != null -> {
                // 错误状态
                ErrorView(
                    error = uiState.error!!,
                    onRetry = { viewModel.refreshAllData() }
                )
            }
            
            uiState.devices.isEmpty() -> {
                // 无设备状态
                NoDevicesView()
            }
            
            else -> {
                // 主要内容
                VideoContentView(
                    uiState = uiState,
                    onDateSelected = { date -> viewModel.selectDate(date) },
                    onVideoClick = { segment -> viewModel.togglePlayback(segment) },
                    onVideoError = { error -> /* 处理视频播放错误 */ },
                    viewModel = viewModel // 传递viewModel
                )
            }
        }
    }
}

/**
 * 顶部标题栏
 */
@Composable
private fun CareTopBar(
    onRefresh: () -> Unit,
    isRefreshing: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "实时关爱",
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "随时查看猫咪动态",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            IconButton(
                onClick = onRefresh,
                enabled = !isRefreshing
            ) {
                if (isRefreshing) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

/**
 * 视频内容视图
 */
@Composable
private fun VideoContentView(
    uiState: VideoUiState,
    onDateSelected: (Date) -> Unit,
    onVideoClick: (VideoSegmentRecord) -> Unit,
    onVideoError: (String) -> Unit,
    viewModel: VideoViewModel // 新增：传递viewModel以获取认证令牌
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
    ) {
        // 视频播放器区域
        item {
            VideoPlayerSection(
                currentVideo = uiState.currentPlayingVideo,
                isPlaying = uiState.isVideoPlaying,
                onVideoClick = onVideoClick,
                onVideoError = onVideoError,
                viewModel = viewModel // 传递viewModel
            )
        }
        
        // 设备状态卡片
        item {
            DeviceStatusSection(devices = uiState.devices.map { DeviceStatusInfo.fromDeviceResponse(it) })
        }
        
        // 日期选择器
        item {
            DatePickerSection(
                availableDates = uiState.availableDates,
                selectedDate = uiState.selectedDate,
                onDateSelected = onDateSelected
            )
        }
        
        // 每日统计信息
        if (uiState.dailyStats != null) {
            item {
                DailyStatsSection(stats = uiState.dailyStats!!)
            }
        }
        
        // 视频列表
        item {
            VideoListSection(
                videos = uiState.videoSegments,
                currentPlayingVideo = uiState.currentPlayingVideo,
                onVideoClick = onVideoClick
            )
        }
    }
}

/**
 * 视频播放器区域
 */
@Composable
private fun VideoPlayerSection(
    currentVideo: VideoSegmentRecord?,
    isPlaying: Boolean,
    onVideoClick: (VideoSegmentRecord) -> Unit,
    onVideoError: (String) -> Unit,
    viewModel: VideoViewModel // 新增：传递viewModel以获取认证令牌
) {
    // 获取认证令牌
    var authToken by remember { mutableStateOf<String?>(null) }
    
    LaunchedEffect(Unit) {
        authToken = viewModel.getAccessToken()
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "视频监控",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            HLSVideoPlayer(
                videoSegment = currentVideo?.toVideoSegment(),
                isPlaying = isPlaying,
                onPlayClick = { 
                    currentVideo?.let { onVideoClick(it) }
                },
                onVideoError = onVideoError,
                modifier = Modifier.fillMaxWidth(),
                authToken = authToken // 传递认证令牌
            )
        }
    }
}

/**
 * 设备状态区域
 */
@Composable
private fun DeviceStatusSection(devices: List<DeviceStatusInfo>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Devices,
                    contentDescription = "设备状态",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "设备状态",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    text = "${devices.count { it.isOnline }}/${devices.size} 在线",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            if (devices.isEmpty()) {
                Text(
                    text = "暂无可用设备",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            } else {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(devices) { device ->
                        EnhancedDeviceStatusCard(device = device)
                    }
                }
            }
        }
    }
}

/**
 * 增强的设备状态卡片（显示更多信息）
 */
@Composable
private fun EnhancedDeviceStatusCard(device: DeviceStatusInfo) {
    Card(
        modifier = Modifier
            .width(140.dp)
            .height(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (device.isOnline) 
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            else 
                MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // 顶部：在线状态和状态源
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    CircleIndicator(
                        color = if (device.isOnline) Color.Green else Color.Red,
                        size = 6.dp
                    )
                    Text(
                        text = if (device.isOnline) "在线" else "离线",
                        fontSize = 10.sp,
                        color = if (device.isOnline) Color.Green else Color.Red,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // 状态源指示
                Text(
                    text = device.statusSource,
                    fontSize = 8.sp,
                    color = if (device.hasRealTimeData) Color.Green else Color(0xFFFF9800),
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 中间：设备名称
            Text(
                text = device.name,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 2,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            // 底部：传感器数据
            Column(
                verticalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                // 电池电量
                device.batteryLevel?.let { battery ->
                    SensorDataRow(
                        icon = Icons.Default.Battery6Bar,
                        value = "${battery}%",
                        color = when {
                            battery > 50 -> Color.Green
                            battery > 20 -> Color(0xFFFF9800)
                            else -> Color.Red
                        }
                    )
                }
                
                // 温度
                device.temperature?.let { temp ->
                    SensorDataRow(
                        icon = Icons.Default.Thermostat,
                        value = "${temp.toInt()}°C",
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                // 湿度
                device.humidity?.let { humidity ->
                    SensorDataRow(
                        icon = Icons.Default.WaterDrop,
                        value = "${humidity.toInt()}%",
                        color = MaterialTheme.colorScheme.secondary
                    )
                }
            }
        }
    }
}

/**
 * 传感器数据行
 */
@Composable
private fun SensorDataRow(
    icon: ImageVector,
    value: String,
    color: Color
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(12.dp)
        )
        Text(
            text = value,
            fontSize = 10.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

/**
 * 圆形指示器
 */
@Composable
private fun CircleIndicator(color: Color, size: androidx.compose.ui.unit.Dp) {
    Box(
        modifier = Modifier
            .size(size)
            .background(color, shape = androidx.compose.foundation.shape.CircleShape)
    )
}

/**
 * 日期选择器区域
 */
@Composable
private fun DatePickerSection(
    availableDates: List<Date>,
    selectedDate: Date?,
    onDateSelected: (Date) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.DateRange,
                    contentDescription = "日期选择",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "选择日期",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    text = "${availableDates.size} 天有记录",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            if (availableDates.isEmpty()) {
                Text(
                    text = "暂无视频记录",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            } else {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(availableDates) { date ->
                        DateChip(
                            date = date,
                            isSelected = selectedDate == date,
                            onClick = { onDateSelected(date) }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 日期选择芯片
 */
@Composable
private fun DateChip(
    date: Date,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val formatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }
    val dayFormatter = remember { SimpleDateFormat("E", Locale.getDefault()) }
    
    Card(
        modifier = Modifier
            .clickable { onClick() }
            .width(60.dp)
            .height(64.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) 
                MaterialTheme.colorScheme.primary 
            else 
                MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 6.dp else 2.dp
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = dayFormatter.format(date),
                fontSize = 10.sp,
                color = if (isSelected) 
                    MaterialTheme.colorScheme.onPrimary 
                else 
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Text(
                text = formatter.format(date),
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = if (isSelected) 
                    MaterialTheme.colorScheme.onPrimary 
                else 
                    MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

/**
 * 每日统计区域
 */
@Composable
private fun DailyStatsSection(stats: DailyVideoStats) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "今日统计",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    icon = Icons.Default.Videocam,
                    label = "视频数量",
                    value = "${stats.videoCount}个"
                )
                StatItem(
                    icon = Icons.Default.Timer,
                    label = "总时长",
                    value = formatDuration(stats.totalDuration)
                )
                StatItem(
                    icon = Icons.Default.Pets,
                    label = "如厕次数",
                    value = "${stats.segments.count { it.isValidToiletRecord }}次"
                )
            }
        }
    }
}

/**
 * 统计项
 */
@Composable
private fun StatItem(
    icon: ImageVector,
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

/**
 * 视频列表区域
 */
@Composable
private fun VideoListSection(
    videos: List<VideoSegmentRecord>,
    currentPlayingVideo: VideoSegmentRecord?,
    onVideoClick: (VideoSegmentRecord) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.PlayCircleOutline,
                    contentDescription = "视频列表",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "视频列表",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    text = "${videos.size} 个视频",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            if (videos.isEmpty()) {
                Text(
                    text = "当前日期暂无视频记录",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            } else {
                videos.forEach { video ->
                    VideoListItem(
                        video = video,
                        isPlaying = currentPlayingVideo?.id == video.id,
                        onClick = { onVideoClick(video) }
                    )
                    if (video != videos.last()) {
                        Divider(
                            modifier = Modifier.padding(vertical = 8.dp),
                            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 视频列表项
 */
@Composable
private fun VideoListItem(
    video: VideoSegmentRecord,
    isPlaying: Boolean,
    onClick: () -> Unit
) {
    val timeFormatter = remember { SimpleDateFormat("HH:mm:ss", Locale.getDefault()) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 播放图标
        Icon(
            imageVector = if (isPlaying) Icons.Default.PauseCircle else Icons.Default.PlayCircle,
            contentDescription = if (isPlaying) "暂停" else "播放",
            tint = if (isPlaying) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.size(32.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 视频信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = timeFormatter.format(parseVideoStartTime(video.startTime)),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "•",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "设备: ${video.deviceId}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = video.formattedDuration,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                if (video.isPlayable) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "•",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "可播放",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // 播放状态标记
        if (video.isPlayable) {
            Icon(
                imageVector = Icons.Default.PlayCircle,
                contentDescription = "可播放",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 加载视图
 */
@Composable
private fun LoadingView() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "正在加载设备和视频数据...",
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 错误视图
 */
@Composable
private fun ErrorView(
    error: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = "错误",
                    tint = MaterialTheme.colorScheme.error,
                    modifier = Modifier.size(48.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "加载失败",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = onRetry,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("重试")
                }
            }
        }
    }
}

/**
 * 无设备视图
 */
@Composable
private fun NoDevicesView() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.DevicesOther,
                contentDescription = "无设备",
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "暂无可用设备",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "请先添加设备才能查看视频记录",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 格式化时长
 */
private fun formatDuration(totalSeconds: Double): String {
    val hours = (totalSeconds / 3600).toInt()
    val minutes = ((totalSeconds % 3600) / 60).toInt()
    val seconds = (totalSeconds % 60).toInt()
    
    return if (hours > 0) {
        "${hours}小时${minutes}分"
    } else {
        "${minutes}分${seconds}秒"
    }
}

/**
 * 解析视频开始时间
 */
private fun parseVideoStartTime(startTimeStr: String): Date {
    return try {
        val isoFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault()).apply {
            timeZone = TimeZone.getTimeZone("UTC")
        }
        isoFormatter.parse(startTimeStr) ?: Date()
    } catch (e: Exception) {
        Date()
    }
}
