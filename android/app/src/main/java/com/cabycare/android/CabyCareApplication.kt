package com.cabycare.android

import android.app.Application
import android.util.Log
import dagger.hilt.android.HiltAndroidApp

/**
 * CabyCare应用程序入口点
 * 负责应用程序的全局初始化和配置
 */
@HiltAndroidApp
class CabyCareApplication : Application() {
    
    companion object {
        private const val TAG = "CabyCareApp"
    }
    
    override fun onCreate() {
        super.onCreate()
        
        Log.i(TAG, "📱 CabyCare应用启动")
        
        // 初始化应用程序组件
        initializeComponents()
    }
    
    /**
     * 初始化应用程序组件
     */
    private fun initializeComponents() {
        // 这里可以添加全局初始化逻辑
        // 例如：崩溃报告、分析工具、日志系统等
        Log.d(TAG, "✅ 应用程序组件初始化完成")
        Log.d(TAG, "💾 视频缓存系统已就绪")
    }
}
