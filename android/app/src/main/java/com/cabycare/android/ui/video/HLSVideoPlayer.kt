package com.cabycare.android.ui.video

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.VideoCall
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.MimeTypes
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.ui.PlayerView
import com.cabycare.android.data.model.VideoSegment
import android.util.Log
import androidx.media3.exoplayer.hls.HlsMediaSource
import androidx.media3.exoplayer.hls.playlist.DefaultHlsPlaylistParserFactory
import androidx.media3.exoplayer.hls.playlist.DefaultHlsPlaylistTracker
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.LoadControl
import androidx.media3.exoplayer.upstream.DefaultAllocator
import androidx.media3.exoplayer.upstream.DefaultLoadErrorHandlingPolicy
import androidx.media3.exoplayer.DefaultRenderersFactory
import androidx.media3.common.C
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember

/**
 * HLS视频播放器组件
 * 支持HLS流媒体播放，使用ExoPlayer
 */
@Composable
fun HLSVideoPlayer(
    videoSegment: VideoSegment?,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    onVideoError: (String) -> Unit,
    modifier: Modifier = Modifier,
    authToken: String? = null // 新增：通过参数传递认证令牌
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            if (videoSegment != null) {
                // 视频播放区域
                ExoPlayerVideoView(
                    videoSegment = videoSegment,
                    isPlaying = isPlaying,
                    onPlayClick = onPlayClick,
                    onVideoError = onVideoError,
                    authToken = authToken // 传递认证令牌
                )
            } else {
                // 无视频状态
                NoVideoContent()
            }
        }
    }
}

/**
 * ExoPlayer视频播放组件
 */
@Composable
private fun ExoPlayerVideoView(
    videoSegment: VideoSegment,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    onVideoError: (String) -> Unit,
    authToken: String? = null // 新增：通过参数传递认证令牌
) {
    val context = LocalContext.current
    var player by remember { mutableStateOf<ExoPlayer?>(null) }
    var showControls by remember { mutableStateOf(true) }
    var isBuffering by remember { mutableStateOf(false) }
    var bufferPercentage by remember { mutableIntStateOf(0) }
    var bufferedPosition by remember { mutableLongStateOf(0L) }

    // 🚀 自适应缓冲管理器
    val adaptiveBufferManager = remember { AdaptiveBufferManager(context) }

    // 监听网络状态变化
    LaunchedEffect(Unit) {
        while (true) {
            adaptiveBufferManager.updateNetworkStatus()
            kotlinx.coroutines.delay(30000) // 每30秒检查一次网络状态
        }
    }
    
    // 🎯 新增：缓冲监控状态
    var currentPosition by remember { mutableLongStateOf(0L) }
    var totalDuration by remember { mutableLongStateOf(0L) }
    
    // 🚀 智能缓冲状态监控
    LaunchedEffect(player) {
        player?.let { exoPlayer ->
            var lastBufferWarningTime = 0L
            var consecutiveLowBufferCount = 0

            while (true) {
                try {
                    val buffered = exoPlayer.bufferedPercentage
                    val bufferedPos = exoPlayer.bufferedPosition
                    val currentPos = exoPlayer.currentPosition
                    val duration = exoPlayer.duration
                    val isPlaying = exoPlayer.isPlaying

                    // 更新状态
                    bufferPercentage = buffered
                    bufferedPosition = bufferedPos
                    currentPosition = currentPos
                    totalDuration = duration

                    // 智能缓冲监控
                    if (buffered > 0 && isPlaying) {
                        val bufferAhead = (bufferedPos - currentPos) / 1000
                        val currentTime = System.currentTimeMillis()

                        // 缓冲不足检测
                        if (bufferAhead < 3) {
                            consecutiveLowBufferCount++

                            // 避免频繁日志，每10秒最多警告一次
                            if (currentTime - lastBufferWarningTime > 10000) {
                                Log.w("HLSVideoPlayer", "⚠️ 缓冲严重不足: 仅剩${bufferAhead}秒，连续低缓冲次数: $consecutiveLowBufferCount")
                                lastBufferWarningTime = currentTime
                            }

                            // 连续低缓冲超过3次，使用自适应缓冲管理器调整策略
                            if (consecutiveLowBufferCount >= 3) {
                                Log.w("HLSVideoPlayer", "🔄 检测到持续缓冲问题，触发自适应调整")
                                adaptiveBufferManager.adjustBufferStrategy(buffered, bufferAhead * 1000, consecutiveLowBufferCount)
                                consecutiveLowBufferCount = 0 // 重置计数器
                            }
                        } else {
                            consecutiveLowBufferCount = 0 // 重置计数器

                            // 定期记录正常状态（降低频率）
                            if (currentTime - lastBufferWarningTime > 30000) {
                                val bufferedSeconds = bufferedPos / 1000
                                val currentSeconds = currentPos / 1000
                                Log.d("HLSVideoPlayer", "📊 缓冲正常: ${buffered}% | 当前: ${currentSeconds}s | 预缓冲: ${bufferAhead}s")
                                lastBufferWarningTime = currentTime
                            }
                        }
                    }

                } catch (e: Exception) {
                    Log.e("HLSVideoPlayer", "缓冲监控异常: ${e.message}")
                }

                // 动态调整监控频率：播放时更频繁，暂停时较少
                val monitorInterval = if (exoPlayer.isPlaying) 3000L else 10000L
                kotlinx.coroutines.delay(monitorInterval)
            }
        }
    }
    
    // 创建HTTP数据源工厂（记忆变量，用于在多个LaunchedEffect间共享）
    val httpDataSourceFactory = remember {
        DefaultHttpDataSource.Factory().apply {
            // 设置用户代理
            setUserAgent("CabyCare-Android/1.0")
            
            // 认证头将在下面统一设置
            
            // 🚀 优化网络连接设置
            setConnectTimeoutMs(8000)  // 8秒连接超时（更快的失败检测）
            setReadTimeoutMs(15000)    // 15秒读取超时（适中的读取时间）

            // 允许跨协议重定向
            setAllowCrossProtocolRedirects(true)

            // 🎯 设置保持连接活跃
            setKeepPostFor302Redirects(true)

            // 添加网络优化头
            val existingProps = mutableMapOf<String, String>()
            if (authToken != null) {
                existingProps["Authorization"] = "Bearer $authToken"
            }
            existingProps.putAll(mapOf(
                "Connection" to "keep-alive",
                "Cache-Control" to "no-cache",
                "Accept-Encoding" to "gzip, deflate"
            ))
            setDefaultRequestProperties(existingProps)

            if (authToken != null) {
                Log.d("HLSVideoPlayer", "已设置认证头用于HLS播放")
            } else {
                Log.w("HLSVideoPlayer", "警告：没有提供访问令牌，可能无法播放需要认证的视频")
            }
        }
    }
    
    // 初始化ExoPlayer
    LaunchedEffect(Unit) {
        try {
            // 使用常规context，避免attribution问题
            val playbackContext = context
            
            // 🔧 创建自定义渲染器工厂来处理 MediaCodec 查询问题
            val renderersFactory = DefaultRenderersFactory(playbackContext).apply {
                // 设置扩展渲染器模式为优先使用
                setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER)
                // 启用 MediaCodec 异步处理来减少阻塞
                setEnableDecoderFallback(true)
                // 允许音频处理器链
                setEnableAudioFloatOutput(false)
            }
            
            // 创建媒体源工厂
            val mediaSourceFactory = DefaultMediaSourceFactory(httpDataSourceFactory)
            
            // 🚀 使用自适应缓冲管理器创建LoadControl
            adaptiveBufferManager.updateNetworkStatus()
            val loadControl = adaptiveBufferManager.createAdaptiveLoadControl()
            
            // 初始化ExoPlayer并配置自定义LoadControl
            player = ExoPlayer.Builder(playbackContext)
                .setRenderersFactory(renderersFactory)  // 使用自定义渲染器工厂
                .setMediaSourceFactory(mediaSourceFactory)
                .setLoadControl(loadControl)  // 应用自定义缓冲控制
                .build().apply {
                    addListener(object : Player.Listener {
                        override fun onPlayerError(error: PlaybackException) {
                            // 过滤掉系统级 MediaCodec 查询错误，只记录真正的播放错误
                            val isSystemResourceError = error.message?.contains("Failed to query component interface") == true ||
                                                       error.message?.contains("BAD_INDEX") == true
                            
                            if (isSystemResourceError) {
                                Log.d("HLSVideoPlayer", "系统级 MediaCodec 查询问题，通常不影响播放: ${error.message}")
                            } else {
                                Log.e("HLSVideoPlayer", "播放错误: ${error.message}")
                                Log.e("HLSVideoPlayer", "错误详情: ${error.cause}")
                                Log.e("HLSVideoPlayer", "错误代码: ${error.errorCode}")
                                onVideoError("视频播放失败: ${error.message}")
                            }
                        }
                        
                        override fun onPlaybackStateChanged(playbackState: Int) {
                            when (playbackState) {
                                Player.STATE_READY -> {
                                    isBuffering = false
                                    bufferPercentage = bufferedPercentage
                                    bufferedPosition = bufferedPosition
                                    Log.d("HLSVideoPlayer", "视频准备就绪，缓冲充足 (${bufferPercentage}%)")
                                    showControls = false
                                }
                                Player.STATE_BUFFERING -> {
                                    isBuffering = true
                                    bufferPercentage = bufferedPercentage
                                    bufferedPosition = bufferedPosition
                                    Log.d("HLSVideoPlayer", "视频缓冲中... 缓冲百分比: ${bufferPercentage}%")
                                }
                                Player.STATE_ENDED -> {
                                    isBuffering = false
                                    Log.d("HLSVideoPlayer", "视频播放结束")
                                    showControls = true
                                }
                                Player.STATE_IDLE -> {
                                    isBuffering = false
                                    Log.d("HLSVideoPlayer", "播放器空闲状态")
                                }
                            }
                        }
                        
                        override fun onIsLoadingChanged(isLoading: Boolean) {
                            if (isLoading) {
                                bufferPercentage = bufferedPercentage
                                bufferedPosition = bufferedPosition
                            }
                            Log.d("HLSVideoPlayer", "加载状态变化: $isLoading, 缓冲位置: ${bufferedPosition}ms, 缓冲百分比: ${bufferPercentage}%")
                        }
                        
                        override fun onMediaItemTransition(mediaItem: androidx.media3.common.MediaItem?, reason: Int) {
                            Log.d("HLSVideoPlayer", "媒体项切换: ${mediaItem?.localConfiguration?.uri}")
                        }
                    })
                }
                
            Log.d("HLSVideoPlayer", "ExoPlayer初始化成功，已配置优化缓冲策略")
            
        } catch (e: Exception) {
            Log.e("HLSVideoPlayer", "初始化播放器失败", e)
            onVideoError("播放器初始化失败: ${e.message}")
        }
    }
    
    // 更新视频URL和播放状态
    LaunchedEffect(videoSegment.fileUrl, isPlaying) {
        player?.let { exoPlayer ->
            try {
                Log.d("HLSVideoPlayer", "准备加载HLS视频: ${videoSegment.fileUrl}")
                
                // 明确指定为 HLS 内容
                val mediaItem = MediaItem.Builder()
                    .setUri(videoSegment.fileUrl)
                    .setMimeType(MimeTypes.APPLICATION_M3U8)
                    .build()
                
                // 🚀 优化 HLS MediaSource 配置
                val hlsMediaSource = HlsMediaSource.Factory(httpDataSourceFactory)
                    .setAllowChunklessPreparation(true)  // 允许无chunk准备，更快开始播放
                    .setLoadErrorHandlingPolicy(
                        DefaultLoadErrorHandlingPolicy().apply {
                            // 自定义错误处理：网络错误重试3次，其他错误重试1次
                        }
                    )
                    .setPlaylistParserFactory(DefaultHlsPlaylistParserFactory())  // 使用默认播放列表解析器
                    .setPlaylistTrackerFactory { dataSourceFactory, loadErrorHandlingPolicy, playlistParserFactory ->
                        DefaultHlsPlaylistTracker(
                            dataSourceFactory,
                            loadErrorHandlingPolicy,
                            playlistParserFactory
                        ).apply {
                            // 设置播放列表刷新间隔（对于VOD内容不会频繁刷新）
                        }
                    }
                    .createMediaSource(mediaItem)
                
                // 设置媒体源
                exoPlayer.setMediaSource(hlsMediaSource)
                exoPlayer.prepare()
                
                // 🎯 设置播放参数来优化缓冲体验
                exoPlayer.setWakeMode(C.WAKE_MODE_NETWORK)  // 保持网络唤醒
                
                // 根据isPlaying状态控制播放
                if (isPlaying) {
                    exoPlayer.play()
                    Log.d("HLSVideoPlayer", "开始播放HLS视频，当前缓冲: ${exoPlayer.bufferedPercentage}%")
                } else {
                    exoPlayer.pause()
                    Log.d("HLSVideoPlayer", "暂停播放HLS视频")
                }
                
            } catch (e: Exception) {
                Log.e("HLSVideoPlayer", "设置HLS媒体项失败", e)
                onVideoError("加载HLS视频失败: ${e.message}")
            }
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            player?.release()
            player = null
            Log.d("HLSVideoPlayer", "释放播放器资源")
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // ExoPlayer视图
        AndroidView(
            factory = { ctx ->
                PlayerView(ctx).apply {
                    this.player = player
                    useController = false // 禁用默认控制器，使用自定义控制器
                    setBackgroundColor(android.graphics.Color.BLACK)
                }
            },
            update = { playerView ->
                playerView.player = player
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // 缓冲状态指示器
        if (isBuffering) {
            BufferingOverlay(
                bufferPercentage = bufferPercentage,
                modifier = Modifier.align(Alignment.Center)
            )
        }
        
        // 自定义播放控制器（在视频上方显示）
        if (showControls) {
            VideoControlOverlay(
                videoSegment = videoSegment,
                isPlaying = isPlaying,
                onPlayClick = onPlayClick,
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 视频信息覆盖层（包含缓冲信息）
        VideoInfoOverlay(
            videoSegment = videoSegment,
            bufferPercentage = bufferPercentage,
            bufferedPosition = bufferedPosition,
            currentPosition = currentPosition,
            totalDuration = totalDuration,
            isBuffering = isBuffering,
            modifier = Modifier.align(Alignment.BottomStart)
        )
        
        // 点击区域来显示/隐藏控制器
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable {
                    showControls = !showControls
                }
        )
    }
}

/**
 * 视频控制覆盖层
 */
@Composable
private fun VideoControlOverlay(
    videoSegment: VideoSegment,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(Color.Black.copy(alpha = 0.3f)),
        contentAlignment = Alignment.Center
    ) {
        // 播放/暂停按钮
        IconButton(
            onClick = onPlayClick,
            modifier = Modifier.size(64.dp)
        ) {
            Icon(
                imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                contentDescription = if (isPlaying) "暂停" else "播放",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

/**
 * 视频信息覆盖层
 */
@Composable
private fun VideoInfoOverlay(
    videoSegment: VideoSegment,
    bufferPercentage: Int = 0,
    bufferedPosition: Long = 0L,
    currentPosition: Long = 0L,
    totalDuration: Long = 0L,
    isBuffering: Boolean = false,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .padding(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(6.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = "设备: ${videoSegment.deviceId}",
                color = Color.White,
                fontSize = 12.sp
            )
            Text(
                text = "时长: ${videoSegment.formattedDuration}",
                color = Color.White,
                fontSize = 12.sp
            )
            
            // 播放进度信息
            if (currentPosition > 0 || totalDuration > 0) {
                val currentSec = currentPosition / 1000
                val totalSec = totalDuration / 1000
                val progressPercent = if (totalSec > 0) (currentSec * 100 / totalSec).toInt() else 0
                
                Text(
                    text = "进度: ${currentSec}s/${totalSec}s (${progressPercent}%)",
                    color = Color.Cyan.copy(alpha = 0.9f),
                    fontSize = 10.sp
                )
            }
            
            // 缓冲信息
            if (bufferPercentage > 0 || bufferedPosition > 0) {
                val bufferAhead = (bufferedPosition - currentPosition) / 1000
                val statusColor = when {
                    isBuffering -> Color.Yellow
                    bufferAhead < 5 -> Color.Red
                    bufferAhead < 15 -> Color(0xFFFF9800) // Orange color
                    else -> Color.Green
                }
                
                Text(
                    text = "缓冲: ${bufferPercentage}% (+${bufferAhead}s)",
                    color = statusColor.copy(alpha = 0.8f),
                    fontSize = 10.sp
                )
            }
            
            // 缓冲状态指示
            if (isBuffering) {
                Text(
                    text = "🔄 正在缓冲...",
                    color = Color.Yellow,
                    fontSize = 10.sp
                )
            }
            
            // 显示播放URL的一部分（用于调试）
            Text(
                text = "URL: ${videoSegment.fileUrl.takeLast(30)}",
                color = Color.White.copy(alpha = 0.7f),
                fontSize = 10.sp
            )
        }
    }
}

/**
 * 无视频内容显示
 */
@Composable
private fun NoVideoContent() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.VideoCall,
            contentDescription = "暂无视频",
            tint = Color.White.copy(alpha = 0.6f),
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "请选择视频播放",
            color = Color.White.copy(alpha = 0.8f),
            fontSize = 14.sp
        )
    }
}

/**
 * 缓冲状态覆盖层
 */
@Composable
private fun BufferingOverlay(
    bufferPercentage: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(20.dp)
        ) {
            CircularProgressIndicator(
                color = Color.White,
                strokeWidth = 3.dp,
                modifier = Modifier.size(40.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "缓冲中...",
                color = Color.White,
                fontSize = 14.sp
            )
            
            if (bufferPercentage > 0) {
                Text(
                    text = "$bufferPercentage%",
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 12.sp
                )
            }
        }
    }
} 