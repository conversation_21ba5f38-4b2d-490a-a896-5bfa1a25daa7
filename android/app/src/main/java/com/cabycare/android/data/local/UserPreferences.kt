package com.cabycare.android.data.local

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好设置管理器
 * 使用DataStore存储用户的偏好设置和认证信息
 */
@Singleton
class UserPreferences @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val PREFERENCES_NAME = "cabycare_preferences"
        
        // 认证相关键
        private val ACCESS_TOKEN_KEY = stringPreferencesKey("access_token")
        private val REFRESH_TOKEN_KEY = stringPreferencesKey("refresh_token")
        private val ID_TOKEN_KEY = stringPreferencesKey("id_token")
        private val USER_ID_KEY = stringPreferencesKey("user_id") // 后端数据库中的用户ID
        private val LOGTO_ID_KEY = stringPreferencesKey("logto_id") // Logto认证服务的用户ID
        private val USER_EMAIL_KEY = stringPreferencesKey("user_email")
        private val USER_NAME_KEY = stringPreferencesKey("user_name")
        private val IS_AUTHENTICATED_KEY = booleanPreferencesKey("is_authenticated")
        
        // 应用设置键
        private val THEME_MODE_KEY = stringPreferencesKey("theme_mode") // "light", "dark", "system"
        private val LANGUAGE_KEY = stringPreferencesKey("language") // "zh", "en"
        private val NOTIFICATION_ENABLED_KEY = booleanPreferencesKey("notification_enabled")
        private val VIDEO_QUALITY_KEY = stringPreferencesKey("video_quality")
        private val AUTO_PLAY_KEY = booleanPreferencesKey("auto_play")
        
        // 缓存相关键
        private val LAST_SYNC_TIME_KEY = stringPreferencesKey("last_sync_time")
        private val CACHE_SIZE_LIMIT_KEY = stringPreferencesKey("cache_size_limit")

        // OAuth相关键
        private val AUTH_STATE_KEY = stringPreferencesKey("auth_state")
        private val CODE_VERIFIER_KEY = stringPreferencesKey("code_verifier")

        // 令牌过期时间键
        private val TOKEN_EXPIRES_AT_KEY = longPreferencesKey("token_expires_at")
    }
    
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
        name = PREFERENCES_NAME
    )
    
    // MARK: - 认证相关方法
    
    /**
     * 保存访问令牌
     */
    suspend fun saveAccessToken(token: String) {
        context.dataStore.edit { preferences ->
            preferences[ACCESS_TOKEN_KEY] = token
        }
    }
    
    /**
     * 获取访问令牌
     */
    fun getAccessToken(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[ACCESS_TOKEN_KEY]
        }
    }
    
    /**
     * 保存刷新令牌
     */
    suspend fun saveRefreshToken(token: String) {
        context.dataStore.edit { preferences ->
            preferences[REFRESH_TOKEN_KEY] = token
        }
    }
    
    /**
     * 获取刷新令牌
     */
    fun getRefreshToken(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[REFRESH_TOKEN_KEY]
        }
    }

    /**
     * 保存令牌过期时间
     */
    suspend fun saveTokenExpiresAt(expiresAt: Long) {
        context.dataStore.edit { preferences ->
            preferences[TOKEN_EXPIRES_AT_KEY] = expiresAt
        }
    }

    /**
     * 获取令牌过期时间
     */
    fun getTokenExpiresAt(): Flow<Long?> {
        return context.dataStore.data.map { preferences ->
            preferences[TOKEN_EXPIRES_AT_KEY]
        }
    }

    /**
     * 调试方法：打印所有存储的认证数据
     */
    suspend fun printAuthData() {
        try {
            val preferences = context.dataStore.data.first()
            android.util.Log.d("UserPreferences", "=== 存储的认证数据 ===")

            // 安全处理令牌显示，只显示前20个字符
            val accessToken = preferences[ACCESS_TOKEN_KEY]
            val refreshToken = preferences[REFRESH_TOKEN_KEY]
            val idToken = preferences[ID_TOKEN_KEY]

            android.util.Log.d("UserPreferences", "ACCESS_TOKEN: ${if (accessToken != null && accessToken.isNotEmpty()) accessToken.substring(0, minOf(20, accessToken.length)) + "..." else "null"}")
            android.util.Log.d("UserPreferences", "REFRESH_TOKEN: ${if (refreshToken != null && refreshToken.isNotEmpty()) refreshToken.substring(0, minOf(20, refreshToken.length)) + "..." else "null"}")
            android.util.Log.d("UserPreferences", "ID_TOKEN: ${if (idToken != null && idToken.isNotEmpty()) idToken.substring(0, minOf(20, idToken.length)) + "..." else "null"}")

            android.util.Log.d("UserPreferences", "USER_ID: ${preferences[USER_ID_KEY]}")
            android.util.Log.d("UserPreferences", "LOGTO_ID: ${preferences[LOGTO_ID_KEY]}")
            android.util.Log.d("UserPreferences", "USER_EMAIL: ${preferences[USER_EMAIL_KEY]}")
            android.util.Log.d("UserPreferences", "USER_NAME: ${preferences[USER_NAME_KEY]}")
            android.util.Log.d("UserPreferences", "IS_AUTHENTICATED: ${preferences[IS_AUTHENTICATED_KEY]}")

            // 显示过期时间的可读格式
            val expiresAt = preferences[TOKEN_EXPIRES_AT_KEY]
            if (expiresAt != null) {
                val date = java.util.Date(expiresAt)
                android.util.Log.d("UserPreferences", "TOKEN_EXPIRES_AT: $expiresAt (${date})")
            } else {
                android.util.Log.d("UserPreferences", "TOKEN_EXPIRES_AT: null")
            }

            android.util.Log.d("UserPreferences", "==================")
        } catch (e: Exception) {
            android.util.Log.e("UserPreferences", "打印认证数据失败: ${e.message}")
        }
    }
    
    /**
     * 保存ID令牌
     */
    suspend fun saveIdToken(token: String) {
        context.dataStore.edit { preferences ->
            preferences[ID_TOKEN_KEY] = token
        }
    }

    /**
     * 保存用户ID（后端数据库中的用户ID）
     */
    suspend fun saveUserId(userId: String) {
        context.dataStore.edit { preferences ->
            preferences[USER_ID_KEY] = userId
        }
    }

    /**
     * 保存Logto ID（Logto认证服务的用户ID）
     */
    suspend fun saveLogtoId(logtoId: String) {
        context.dataStore.edit { preferences ->
            preferences[LOGTO_ID_KEY] = logtoId
        }
    }

    /**
     * 保存用户邮箱
     */
    suspend fun saveUserEmail(email: String) {
        context.dataStore.edit { preferences ->
            preferences[USER_EMAIL_KEY] = email
        }
    }

    /**
     * 保存用户姓名
     */
    suspend fun saveUserName(name: String) {
        context.dataStore.edit { preferences ->
            preferences[USER_NAME_KEY] = name
        }
    }

    /**
     * 保存用户信息
     */
    suspend fun saveUserInfo(userId: String, email: String, name: String) {
        context.dataStore.edit { preferences ->
            preferences[USER_ID_KEY] = userId
            preferences[USER_EMAIL_KEY] = email
            preferences[USER_NAME_KEY] = name
            preferences[IS_AUTHENTICATED_KEY] = true
        }
    }
    
    /**
     * 获取用户ID（后端数据库中的用户ID，用于API请求）
     */
    fun getUserId(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[USER_ID_KEY]
        }
    }

    /**
     * 获取Logto ID（Logto认证服务的用户ID）
     */
    fun getLogtoId(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[LOGTO_ID_KEY]
        }
    }
    
    /**
     * 获取用户邮箱
     */
    fun getUserEmail(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[USER_EMAIL_KEY]
        }
    }
    
    /**
     * 获取用户名
     */
    fun getUserName(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[USER_NAME_KEY]
        }
    }
    
    /**
     * 检查是否已认证
     */
    fun isAuthenticated(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[IS_AUTHENTICATED_KEY] ?: false
        }
    }
    
    /**
     * 清除认证信息
     */
    suspend fun clearAuthCredentials() {
        context.dataStore.edit { preferences ->
            preferences.remove(ACCESS_TOKEN_KEY)
            preferences.remove(REFRESH_TOKEN_KEY)
            preferences.remove(USER_ID_KEY)
            preferences.remove(USER_EMAIL_KEY)
            preferences.remove(USER_NAME_KEY)
            preferences.remove(ID_TOKEN_KEY)
            preferences.remove(LOGTO_ID_KEY)
            preferences.remove(AUTH_STATE_KEY)
            preferences.remove(CODE_VERIFIER_KEY)
            preferences.remove(TOKEN_EXPIRES_AT_KEY)  // 清除令牌过期时间
            preferences[IS_AUTHENTICATED_KEY] = false
        }
    }
    
    // MARK: - 应用设置方法
    
    /**
     * 保存主题模式
     */
    suspend fun saveThemeMode(mode: String) {
        context.dataStore.edit { preferences ->
            preferences[THEME_MODE_KEY] = mode
        }
    }
    
    /**
     * 获取主题模式
     */
    fun getThemeMode(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[THEME_MODE_KEY] ?: "system"
        }
    }
    
    /**
     * 保存语言设置
     */
    suspend fun saveLanguage(language: String) {
        context.dataStore.edit { preferences ->
            preferences[LANGUAGE_KEY] = language
        }
    }
    
    /**
     * 获取语言设置
     */
    fun getLanguage(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[LANGUAGE_KEY] ?: "zh"
        }
    }
    
    /**
     * 保存通知开关状态
     */
    suspend fun saveNotificationEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[NOTIFICATION_ENABLED_KEY] = enabled
        }
    }
    
    /**
     * 获取通知开关状态
     */
    fun isNotificationEnabled(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[NOTIFICATION_ENABLED_KEY] ?: true
        }
    }
    
    /**
     * 保存视频质量设置
     */
    suspend fun saveVideoQuality(quality: String) {
        context.dataStore.edit { preferences ->
            preferences[VIDEO_QUALITY_KEY] = quality
        }
    }
    
    /**
     * 获取视频质量设置
     */
    fun getVideoQuality(): Flow<String> {
        return context.dataStore.data.map { preferences ->
            preferences[VIDEO_QUALITY_KEY] ?: "medium"
        }
    }
    
    /**
     * 保存自动播放设置
     */
    suspend fun saveAutoPlay(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_PLAY_KEY] = enabled
        }
    }
    
    /**
     * 获取自动播放设置
     */
    fun isAutoPlayEnabled(): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[AUTO_PLAY_KEY] ?: true
        }
    }
    
    // MARK: - 缓存相关方法
    
    /**
     * 保存最后同步时间
     */
    suspend fun saveLastSyncTime(time: String) {
        context.dataStore.edit { preferences ->
            preferences[LAST_SYNC_TIME_KEY] = time
        }
    }
    
    /**
     * 获取最后同步时间
     */
    fun getLastSyncTime(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[LAST_SYNC_TIME_KEY]
        }
    }

    // MARK: - OAuth相关方法

    /**
     * 保存OAuth状态
     */
    suspend fun saveAuthState(state: String) {
        context.dataStore.edit { preferences ->
            preferences[AUTH_STATE_KEY] = state
        }
    }

    /**
     * 获取OAuth状态
     */
    fun getAuthState(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[AUTH_STATE_KEY]
        }
    }

    /**
     * 保存Code Verifier
     */
    suspend fun saveCodeVerifier(codeVerifier: String) {
        context.dataStore.edit { preferences ->
            preferences[CODE_VERIFIER_KEY] = codeVerifier
        }
    }

    /**
     * 获取Code Verifier
     */
    fun getCodeVerifier(): Flow<String?> {
        return context.dataStore.data.map { preferences ->
            preferences[CODE_VERIFIER_KEY]
        }
    }
}
