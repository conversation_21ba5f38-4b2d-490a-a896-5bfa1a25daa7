package com.cabycare.android

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.ui.Modifier
import com.cabycare.android.data.auth.AuthManager
import com.cabycare.android.ui.CabyCareApp
import com.cabycare.android.ui.theme.CabyCareTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主Activity - 应用程序的入口点
 * 负责设置Compose UI和主题，以及处理Logto回调
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var authManager: AuthManager

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 处理Intent（包括Logto回调）
        handleIntent(intent)

        // 应用启动时立即检查令牌状态
        Log.i(TAG, "📱 应用启动，检查令牌状态")
        // AuthManager在初始化时会自动检查令牌状态，无需额外调用

        setContent {
            CabyCareTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    CabyCareApp(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        intent?.let {
            val action = it.action
            val data = it.data

            Log.d(TAG, "处理Intent: action=$action, data=$data")

            if (action == Intent.ACTION_VIEW && data != null) {
                handleLogtoCallback(data)
            }
        }
    }

    private fun handleLogtoCallback(uri: Uri) {
        Log.i(TAG, "🔄 处理Logto回调: $uri")

        if (uri.scheme == "com.cabycare.android") {
            Log.i(TAG, "✅ 检测到有效的Logto回调URI")

            CoroutineScope(Dispatchers.Main).launch {
                try {
                    // 从URI中提取授权码和状态
                    val code = uri.getQueryParameter("code")
                    val state = uri.getQueryParameter("state")

                    if (code != null && state != null) {
                        Log.i(TAG, "📝 提取到授权码和状态: code=${code.take(10)}..., state=$state")
                        val result = authManager.handleAuthCallback(code, state)
                        when (result) {
                            is com.cabycare.android.data.network.NetworkResult.Success -> {
                                Log.i(TAG, "✅ Logto回调处理成功")
                            }
                            is com.cabycare.android.data.network.NetworkResult.Error -> {
                                Log.e(TAG, "❌ Logto回调处理失败: ${result.exception.message}")
                            }
                            is com.cabycare.android.data.network.NetworkResult.Loading -> {
                                Log.d(TAG, "🔄 正在处理回调...")
                            }
                        }
                    } else {
                        Log.e(TAG, "❌ 回调URI中缺少必要参数: code=$code, state=$state")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 处理Logto回调时发生异常", e)
                }
            }
        } else {
            Log.w(TAG, "⚠️ 无效的回调URI: $uri")
        }
    }
}
