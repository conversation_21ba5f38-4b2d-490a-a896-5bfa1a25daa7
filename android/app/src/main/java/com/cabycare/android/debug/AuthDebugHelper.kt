package com.cabycare.android.debug

import android.content.Context
import android.util.Log
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking

/**
 * 认证调试助手
 * 用于调试认证相关的存储问题
 */
object AuthDebugHelper {
    private const val TAG = "AuthDebugHelper"
    
    // DataStore实例
    private val Context.dataStore by preferencesDataStore(name = "user_preferences")
    
    // 键定义（与UserPreferences中的键保持一致）
    private val ACCESS_TOKEN_KEY = stringPreferencesKey("access_token")
    private val REFRESH_TOKEN_KEY = stringPreferencesKey("refresh_token")
    private val ID_TOKEN_KEY = stringPreferencesKey("id_token")
    private val USER_ID_KEY = stringPreferencesKey("user_id")
    private val LOGTO_ID_KEY = stringPreferencesKey("logto_id")
    private val USER_EMAIL_KEY = stringPreferencesKey("user_email")
    private val USER_NAME_KEY = stringPreferencesKey("user_name")
    private val IS_AUTHENTICATED_KEY = booleanPreferencesKey("is_authenticated")
    private val AUTH_STATE_KEY = stringPreferencesKey("auth_state")
    private val CODE_VERIFIER_KEY = stringPreferencesKey("code_verifier")
    private val TOKEN_EXPIRES_AT_KEY = longPreferencesKey("token_expires_at")
    
    /**
     * 检查DataStore中的所有认证数据
     */
    fun checkAuthData(context: Context) {
        Log.i(TAG, "🔍 开始检查DataStore中的认证数据...")
        
        try {
            runBlocking {
                val preferences = context.dataStore.data.first()
                
                Log.i(TAG, "=== DataStore认证数据检查 ===")
                
                // 检查基本认证状态
                val isAuthenticated = preferences[IS_AUTHENTICATED_KEY]
                Log.i(TAG, "IS_AUTHENTICATED: $isAuthenticated")
                
                // 检查令牌
                val accessToken = preferences[ACCESS_TOKEN_KEY]
                val refreshToken = preferences[REFRESH_TOKEN_KEY]
                val idToken = preferences[ID_TOKEN_KEY]
                
                Log.i(TAG, "ACCESS_TOKEN存在: ${!accessToken.isNullOrEmpty()}")
                Log.i(TAG, "REFRESH_TOKEN存在: ${!refreshToken.isNullOrEmpty()}")
                Log.i(TAG, "ID_TOKEN存在: ${!idToken.isNullOrEmpty()}")
                
                if (accessToken != null) {
                    Log.i(TAG, "ACCESS_TOKEN前缀: ${accessToken.take(20)}...")
                    Log.i(TAG, "ACCESS_TOKEN长度: ${accessToken.length}")
                }
                
                if (refreshToken != null) {
                    Log.i(TAG, "REFRESH_TOKEN前缀: ${refreshToken.take(20)}...")
                    Log.i(TAG, "REFRESH_TOKEN长度: ${refreshToken.length}")
                }
                
                // 检查用户信息
                val userId = preferences[USER_ID_KEY]
                val logtoId = preferences[LOGTO_ID_KEY]
                val userEmail = preferences[USER_EMAIL_KEY]
                val userName = preferences[USER_NAME_KEY]
                
                Log.i(TAG, "USER_ID: $userId")
                Log.i(TAG, "LOGTO_ID: $logtoId")
                Log.i(TAG, "USER_EMAIL: $userEmail")
                Log.i(TAG, "USER_NAME: $userName")
                
                // 检查过期时间
                val expiresAt = preferences[TOKEN_EXPIRES_AT_KEY]
                if (expiresAt != null) {
                    val date = java.util.Date(expiresAt)
                    val currentTime = System.currentTimeMillis()
                    val isExpired = expiresAt <= currentTime
                    Log.i(TAG, "TOKEN_EXPIRES_AT: $expiresAt")
                    Log.i(TAG, "过期时间: $date")
                    Log.i(TAG, "当前时间: ${java.util.Date(currentTime)}")
                    Log.i(TAG, "是否已过期: $isExpired")
                } else {
                    Log.i(TAG, "TOKEN_EXPIRES_AT: null")
                }
                
                // 检查OAuth状态
                val authState = preferences[AUTH_STATE_KEY]
                val codeVerifier = preferences[CODE_VERIFIER_KEY]
                Log.i(TAG, "AUTH_STATE存在: ${!authState.isNullOrEmpty()}")
                Log.i(TAG, "CODE_VERIFIER存在: ${!codeVerifier.isNullOrEmpty()}")
                
                Log.i(TAG, "=== 检查完成 ===")
                
                // 总结
                val hasValidAuth = isAuthenticated == true && !accessToken.isNullOrEmpty()
                Log.i(TAG, "🎯 认证状态总结: ${if (hasValidAuth) "有效" else "无效"}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 检查认证数据失败: ${e.message}", e)
        }
    }
    
    /**
     * 清除所有认证数据（用于测试）
     */
    fun clearAllAuthData(context: Context) {
        Log.w(TAG, "⚠️ 清除所有认证数据...")
        
        try {
            runBlocking {
                context.dataStore.edit { preferences ->
                    preferences.clear()
                }
                Log.i(TAG, "✅ 所有认证数据已清除")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 清除认证数据失败: ${e.message}", e)
        }
    }
}
