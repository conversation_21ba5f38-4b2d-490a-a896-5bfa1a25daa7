package com.cabycare.android.data.cache

import android.content.Context
import android.util.Log
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.CacheDataSource
import com.cabycare.android.data.cache.VideoCache
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 支持缓存的数据源工厂
 * 为HLS流提供自动缓存功能
 */
@Singleton
class CachedDataSourceFactory @Inject constructor(
    @ApplicationContext private val context: Context,
    private val videoCache: VideoCache
) : DataSource.Factory {

    companion object {
        private const val TAG = "CachedDataSourceFactory"
    }
    
    private var authToken: String? = null
    
    /**
     * 设置认证令牌
     */
    fun setAuthToken(token: String?) {
        this.authToken = token
        Log.d(TAG, if (token != null) "🔑 已设置认证令牌" else "🔓 已清除认证令牌")
    }
    
    /**
     * 创建数据源
     */
    override fun createDataSource(): DataSource {
        // 创建HTTP数据源工厂
        val httpDataSourceFactory = DefaultHttpDataSource.Factory().apply {
            // 设置用户代理
            setUserAgent("CabyCare-Android/1.0")
            
            // 添加认证头（如果有提供）
            if (authToken != null) {
                setDefaultRequestProperties(mapOf(
                    "Authorization" to "Bearer $authToken"
                ))
                Log.d(TAG, "🔑 HTTP数据源已配置认证头")
            }
            
            // 优化网络连接设置
            setConnectTimeoutMs(15000) // 15秒连接超时
            setReadTimeoutMs(30000)    // 30秒读取超时
            setAllowCrossProtocolRedirects(true)
            setKeepPostFor302Redirects(true)
        }
        
        // 创建缓存数据源
        return CacheDataSource.Factory()
            .setCache(videoCache.cache)
            .setUpstreamDataSourceFactory(httpDataSourceFactory)
            .setFlags(CacheDataSource.FLAG_BLOCK_ON_CACHE or CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
            .createDataSource().also {
                Log.d(TAG, "✅ 创建带缓存的数据源")
            }
    }
    
    /**
     * 创建仅用于网络的数据源（不使用缓存）
     */
    fun createNetworkOnlyDataSource(): DataSource {
        return DefaultHttpDataSource.Factory().apply {
            setUserAgent("CabyCare-Android/1.0")
            
            if (authToken != null) {
                setDefaultRequestProperties(mapOf(
                    "Authorization" to "Bearer $authToken"
                ))
            }
            
            setConnectTimeoutMs(15000)
            setReadTimeoutMs(30000)
            setAllowCrossProtocolRedirects(true)
            setKeepPostFor302Redirects(true)
        }.createDataSource().also {
            Log.d(TAG, "🌐 创建仅网络数据源（无缓存）")
        }
    }
}
