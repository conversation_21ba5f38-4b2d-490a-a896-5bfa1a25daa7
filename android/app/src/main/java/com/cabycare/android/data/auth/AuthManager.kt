package com.cabycare.android.data.auth

import android.content.Context
import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.model.OAuthCallbackResponse
import com.cabycare.android.data.model.User
import com.cabycare.android.data.model.UserInfoResponse
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.network.safeApiCall
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.Base64
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import com.cabycare.android.di.BasicOkHttpClient
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.long

/**
 * 认证状态数据类
 */
data class AuthState(
    val isAuthenticated: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val user: User? = null
)

/**
 * 认证管理器
 * 负责用户认证、令牌管理和自动刷新
 */
@Singleton
class AuthManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userPreferences: UserPreferences,
    private val apiService: ApiService,
    @BasicOkHttpClient private val okHttpClient: OkHttpClient,
    private val logtoManager: LogtoManager
) {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    companion object {
        private const val TAG = "AuthManager"
        private const val TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000L // 5分钟
    }
    
    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    private val refreshMutex = Mutex()
    private var refreshTask: kotlinx.coroutines.Deferred<String>? = null
    
    init {
        // 调试信息
        com.cabycare.android.debug.LogtoDebugHelper.printConfig()
        com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()

        // 初始化时检查认证状态
        checkInitialAuthStatus()

        // 添加额外的调试信息
        scope.launch {
            Log.i(TAG, "🔍 AuthManager初始化完成，开始详细检查...")

            try {
                val isAuth = userPreferences.isAuthenticated().first()
                val token = userPreferences.getAccessToken().first()
                val refreshToken = userPreferences.getRefreshToken().first()
                val expiresAt = userPreferences.getTokenExpiresAt().first()

                Log.i(TAG, "🔍 详细检查结果:")
                Log.i(TAG, "  - isAuthenticated: $isAuth")
                Log.i(TAG, "  - hasAccessToken: ${!token.isNullOrEmpty()}")
                Log.i(TAG, "  - hasRefreshToken: ${!refreshToken.isNullOrEmpty()}")
                Log.i(TAG, "  - tokenExpiresAt: $expiresAt")

                if (token != null && token.isNotEmpty()) {
                    Log.i(TAG, "  - accessToken前缀: ${token.take(20)}...")
                    Log.i(TAG, "  - accessToken长度: ${token.length}")
                }

                if (expiresAt != null) {
                    val currentTime = System.currentTimeMillis()
                    val isExpired = expiresAt <= currentTime
                    Log.i(TAG, "  - 过期时间: ${java.util.Date(expiresAt)}")
                    Log.i(TAG, "  - 当前时间: ${java.util.Date(currentTime)}")
                    Log.i(TAG, "  - 是否已过期: $isExpired")
                }

            } catch (e: Exception) {
                Log.e(TAG, "🔍 详细检查失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 检查初始认证状态
     * 应用启动时立即检查令牌状态，如果令牌有效则直接进入主界面
     */
    private fun checkInitialAuthStatus() {
        // 先将认证状态设置为加载中
        updateAuthState(isLoading = true)

        scope.launch {
            Log.i(TAG, "🔍 开始检查初始认证状态")

            // 打印存储的认证数据用于调试
            userPreferences.printAuthData()

            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()
            val refreshToken = userPreferences.getRefreshToken().first()

            Log.d(TAG, "🔍 存储状态检查:")
            Log.d(TAG, "  - isAuthenticated: $isAuthenticated")
            Log.d(TAG, "  - accessToken存在: ${!accessToken.isNullOrEmpty()}")
            Log.d(TAG, "  - refreshToken存在: ${!refreshToken.isNullOrEmpty()}")

            if (accessToken != null) {
                Log.d(TAG, "  - accessToken前缀: ${accessToken.take(20)}...")
            }
            if (refreshToken != null) {
                Log.d(TAG, "  - refreshToken前缀: ${refreshToken.take(20)}...")
            }

            if (isAuthenticated && !accessToken.isNullOrEmpty()) {
                Log.i(TAG, "🔍 检查存储的访问令牌状态")

                // 检查令牌是否有效
                if (isTokenValid(accessToken)) {
                    Log.i(TAG, "🔑 发现有效的访问令牌，用户已登录")
                    updateAuthState(isAuthenticated = true, isLoading = false)
                    loadUserProfile()
                } else if (shouldRefreshToken(accessToken)) {
                    Log.w(TAG, "⚠️ 访问令牌即将过期或已过期，尝试刷新")
                    try {
                        val newToken = refreshToken()
                        Log.i(TAG, "✅ 令牌刷新成功，用户保持登录状态")
                        updateAuthState(isAuthenticated = true, isLoading = false)
                        loadUserProfile()
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 令牌刷新失败，需要重新登录", e)
                        clearAuthCredentials()
                        updateAuthState(isAuthenticated = false, isLoading = false)
                    }
                } else {
                    Log.e(TAG, "❌ 访问令牌无效且无法刷新，清除认证信息")
                    clearAuthCredentials()
                    updateAuthState(isAuthenticated = false, isLoading = false)
                }
            } else {
                if (!isAuthenticated) {
                    Log.i(TAG, "🔒 用户未登录 - isAuthenticated为false")
                } else if (accessToken.isNullOrEmpty()) {
                    Log.i(TAG, "🔒 用户未登录 - accessToken为空")
                }

                Log.w(TAG, "⚠️ 没有找到有效的认证数据")
                Log.w(TAG, "⚠️ 可能的原因:")
                Log.w(TAG, "  1. 用户从未登录过")
                Log.w(TAG, "  2. 应用数据被清除")
                Log.w(TAG, "  3. 令牌存储失败")
                Log.w(TAG, "  4. DataStore读取失败")

                updateAuthState(isAuthenticated = false, isLoading = false)
            }
        }
    }
    
    /**
     * 检查令牌是否有效（不考虑刷新阈值，只检查是否过期）
     */
    private fun isTokenValid(token: String): Boolean {
        Log.i(TAG, "🔍 开始检查令牌有效性...")

        return try {
            val parts = token.split(".")
            if (parts.size != 3) {
                Log.e(TAG, "❌ 令牌格式无效: 不是有效的JWT格式（应有3部分）")
                return false
            }

            Log.d(TAG, "🔍 JWT格式有效，解析payload...")
            val payload = String(Base64.getUrlDecoder().decode(parts[1]))
            val json = Json.parseToJsonElement(payload).jsonObject

            Log.d(TAG, "🔍 JWT payload: $payload")

            val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull()
            if (exp == null) {
                Log.e(TAG, "❌ 令牌格式无效: 无法解析过期时间")
                return false
            }

            val expirationTime = exp * 1000 // 转换为毫秒
            val currentTime = System.currentTimeMillis()

            // 只检查是否过期，不考虑刷新阈值
            val isValid = expirationTime > currentTime

            Log.i(TAG, "🔍 令牌有效性检查结果:")
            Log.i(TAG, "  - 过期时间: ${Date(expirationTime)}")
            Log.i(TAG, "  - 当前时间: ${Date(currentTime)}")
            Log.i(TAG, "  - 是否有效: $isValid")
            Log.i(TAG, "  - 剩余时间: ${(expirationTime - currentTime) / 1000 / 60} 分钟")

            isValid
        } catch (e: Exception) {
            Log.e(TAG, "❌ 令牌验证失败: ${e.message}", e)
            false
        }
    }

    /**
     * 检查令牌是否需要刷新（即将过期但还未过期）
     * 使用存储的过期时间，与iOS版本逻辑一致
     */
    private suspend fun shouldRefreshToken(token: String): Boolean {
        return try {
            // 优先使用存储的过期时间
            val expiresAt = userPreferences.getTokenExpiresAt().first()
            if (expiresAt != null) {
                val currentTime = System.currentTimeMillis()
                val refreshThreshold = 10 * 60 * 1000L // 10分钟，与iOS版本一致
                val shouldRefresh = expiresAt <= currentTime + refreshThreshold

                Log.d(TAG, "🔄 令牌刷新检查: 过期时间=${Date(expiresAt)}, 当前时间=${Date(currentTime)}, 需要刷新=$shouldRefresh")

                return shouldRefresh
            }

            // 如果没有存储的过期时间，回退到JWT解析
            Log.w(TAG, "⚠️ 没有存储的过期时间，回退到JWT解析")
            val parts = token.split(".")
            if (parts.size != 3) return true

            val payload = String(Base64.getUrlDecoder().decode(parts[1]))
            val json = Json.parseToJsonElement(payload).jsonObject
            val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull() ?: return true

            val expirationTime = exp * 1000 // 转换为毫秒
            val currentTime = System.currentTimeMillis()

            // 如果令牌在10分钟内过期，就需要刷新
            val refreshThreshold = 10 * 60 * 1000L // 10分钟
            val shouldRefresh = expirationTime <= currentTime + refreshThreshold

            Log.d(TAG, "🔄 令牌刷新检查(JWT): 过期时间=${Date(expirationTime)}, 需要刷新=$shouldRefresh")

            shouldRefresh
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 令牌检查失败，认为需要刷新", e)
            true // 检查失败时认为需要刷新
        }
    }
    
    /**
     * 开始OAuth2.0登录流程
     */
    suspend fun startLogin(): String {
        Log.i(TAG, "🎯 开始OAuth2.0登录流程")
        updateAuthState(isLoading = true)
        
        return try {
            val authUrl = buildAuthorizationUrl()
            Log.i(TAG, "🔗 生成授权URL: $authUrl")
            authUrl
        } catch (e: Exception) {
            Log.e(TAG, "❌ 生成授权URL失败", e)
            updateAuthState(isLoading = false, error = e.message)
            throw e
        }
    }
    
    /**
     * 构建授权URL
     */
    private fun buildAuthorizationUrl(): String {
        val state = java.util.UUID.randomUUID().toString()
        val codeVerifier = generateCodeVerifier()
        val codeChallenge = generateCodeChallenge(codeVerifier)
        
        // 保存状态和code verifier
        kotlinx.coroutines.runBlocking {
            userPreferences.saveAuthState(state)
            userPreferences.saveCodeVerifier(codeVerifier)
        }
        
        return "${LogtoConfig.LOGTO_ENDPOINT}/oidc/auth?" +
                "client_id=${LogtoConfig.CLIENT_ID}&" +
                "redirect_uri=${LogtoConfig.REDIRECT_URI}&" +
                "response_type=code&" +
                "scope=openid profile email offline_access&" +
                "state=$state&" +
                "code_challenge=$codeChallenge&" +
                "code_challenge_method=S256&" +
                "prompt=consent"
    }
    
    /**
     * 处理授权回调
     */
    suspend fun handleAuthCallback(code: String, state: String): NetworkResult<Unit> {
        Log.i(TAG, "🔄 处理授权回调")
        updateAuthState(isLoading = true)
        
        return try {
            // 验证state
            val savedState = userPreferences.getAuthState().first()
            if (state != savedState) {
                throw SecurityException("State参数不匹配")
            }
            
            // 交换授权码获取令牌
            val tokens = exchangeCodeForTokens(code)

            // 保存令牌
            userPreferences.saveAccessToken(tokens.accessToken)
            tokens.refreshToken?.let { userPreferences.saveRefreshToken(it) }

            // 保存令牌过期时间
            tokens.expiresIn?.let { expiresIn ->
                val expiresAt = System.currentTimeMillis() + (expiresIn * 1000)
                Log.d(TAG, "⏱️ 保存初始令牌过期时间: ${Date(expiresAt)}")
                userPreferences.saveTokenExpiresAt(expiresAt)
            }

            // 调用后端callback API获取user_id
            Log.i(TAG, "🔄 调用后端callback API获取user_id...")
            val callbackResult = handleOAuthCallback(code, state)
            when (callbackResult) {
                is NetworkResult.Success -> {
                    val callbackResponse = callbackResult.data
                    Log.i(TAG, "✅ 后端callback成功，获得user_id: ${callbackResponse.userId}")

                    // 保存后端返回的user_id
                    userPreferences.saveUserId(callbackResponse.userId)

                    // 获取用户信息（这里应该使用后端的user_id）
                    val userResult = getUserProfile()
                    when (userResult) {
                        is NetworkResult.Success -> {
                            val userInfo = userResult.data
                            // 注意：这里不要覆盖user_id，因为我们已经从callback获得了正确的user_id
                            // 但是我们需要设置认证状态，所以使用现有的user_id
                            val currentUserId = callbackResponse.userId
                            userPreferences.saveUserInfo(currentUserId, userInfo.email, userInfo.nickname ?: userInfo.username)

                            // 创建User对象用于状态更新
                            val user = User(
                                userId = currentUserId,
                                username = userInfo.username,
                                email = userInfo.email,
                                phone = "",
                                nickname = userInfo.nickname ?: "",
                                status = 1,
                                createdAt = "",
                                updatedAt = ""
                            )
                            updateAuthState(isAuthenticated = true, user = user, isLoading = false)
                            Log.i(TAG, "✅ 登录成功，user_id: ${callbackResponse.userId}")
                            NetworkResult.Success(Unit)
                        }
                        is NetworkResult.Error -> {
                            Log.e(TAG, "❌ 获取用户信息失败", userResult.exception)
                            clearAuthCredentials()
                            NetworkResult.Error(userResult.exception)
                        }
                        else -> {
                            NetworkResult.Error(Exception("获取用户信息失败"))
                        }
                    }
                }
                is NetworkResult.Error -> {
                    Log.e(TAG, "❌ OAuth回调处理失败", callbackResult.exception)
                    clearAuthCredentials()
                    NetworkResult.Error(callbackResult.exception)
                }
                else -> {
                    Log.e(TAG, "❌ OAuth回调处理失败：未知错误")
                    NetworkResult.Error(Exception("OAuth回调处理失败"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 处理授权回调失败", e)
            updateAuthState(isLoading = false, error = e.message)
            NetworkResult.Error(e)
        }
    }
    
    /**
     * 交换授权码获取令牌
     */
    private suspend fun exchangeCodeForTokens(code: String): TokenResponse {
        val codeVerifier = userPreferences.getCodeVerifier().first()
            ?: throw IllegalStateException("Code verifier not found")
        
        val requestBody = mapOf(
            "grant_type" to "authorization_code",
            "client_id" to LogtoConfig.CLIENT_ID,
            "code" to code,
            "redirect_uri" to LogtoConfig.REDIRECT_URI,
            "code_verifier" to codeVerifier
        )
        
        val json = Json.encodeToString(kotlinx.serialization.serializer(), requestBody)
        val body = json.toRequestBody("application/json".toMediaType())
        
        val request = Request.Builder()
            .url("${LogtoConfig.LOGTO_ENDPOINT}/oidc/token")
            .post(body)
            .build()
        
        val response = okHttpClient.newCall(request).execute()
        if (!response.isSuccessful) {
            throw Exception("Token exchange failed: ${response.code}")
        }
        
        val responseBody = response.body?.string() ?: throw Exception("Empty response body")
        return Json.decodeFromString(TokenResponse.serializer(), responseBody)
    }
    
    /**
     * 检查当前令牌是否需要刷新（公共方法）
     */
    suspend fun isTokenRefreshNeeded(): Boolean {
        return try {
            val accessToken = userPreferences.getAccessToken().first()
            if (accessToken.isNullOrEmpty()) {
                Log.d(TAG, "🔍 访问令牌为空，需要重新登录")
                return false // 没有令牌，需要重新登录而不是刷新
            }

            val needsRefresh = shouldRefreshToken(accessToken)
            Log.d(TAG, "🔍 令牌刷新检查结果: $needsRefresh")
            needsRefresh
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 检查令牌刷新需求失败: ${e.message}")
            true // 检查失败时认为需要刷新
        }
    }

    /**
     * 刷新访问令牌
     */
    suspend fun refreshToken(): String = refreshMutex.withLock {
        Log.i(TAG, "🔄 开始刷新令牌")
        
        // 如果已有刷新任务在进行，等待完成
        refreshTask?.let { task ->
            Log.d(TAG, "🔄 等待现有刷新任务完成")
            return@withLock task.await()
        }
        
        val task = scope.async {
            try {
                val refreshToken = userPreferences.getRefreshToken().first()
                if (refreshToken.isNullOrEmpty()) {
                    Log.e(TAG, "❌ 刷新令牌不存在或为空")
                    throw IllegalStateException("Refresh token not found")
                }

                Log.d(TAG, "🔄 使用刷新令牌: ${refreshToken.take(10)}...")

                // 🔑 使用与iOS版本相同的后端自定义刷新端点和请求格式
                val requestBody = mapOf(
                    "refresh_token" to refreshToken  // 只发送refresh_token，与iOS版本一致
                )

                val json = Json.encodeToString(kotlinx.serialization.serializer(), requestBody)
                val body = json.toRequestBody("application/json".toMediaType())

                Log.d(TAG, "🌐 发送令牌刷新请求到: ${LogtoConfig.REFRESH_URL}")

                val request = Request.Builder()
                    .url(LogtoConfig.REFRESH_URL)  // 使用后端自定义刷新端点
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .addHeader("X-Client-Platform", "Android")  // 添加平台标识
                    .build()

                val response = performRefreshRequestWithRetry(request, retryCount = 3)
                val responseBody = response.body?.string() ?: ""

                Log.d(TAG, "📡 令牌刷新响应: 状态码=${response.code}")

                if (!response.isSuccessful) {
                    Log.e(TAG, "❌ 令牌刷新失败: HTTP ${response.code}")
                    Log.e(TAG, "❌ 响应内容: $responseBody")
                    throw Exception("Token refresh failed: HTTP ${response.code} - $responseBody")
                }

                if (responseBody.isEmpty()) {
                    Log.e(TAG, "❌ 令牌刷新响应为空")
                    throw Exception("Empty response body")
                }

                val tokenResponse = Json.decodeFromString(TokenResponse.serializer(), responseBody)

                // 验证新令牌
                if (tokenResponse.accessToken.isEmpty()) {
                    Log.e(TAG, "❌ 新访问令牌为空")
                    throw Exception("New access token is empty")
                }

                // 保存新令牌
                Log.d(TAG, "💾 保存新的访问令牌: ${tokenResponse.accessToken.take(10)}...")
                userPreferences.saveAccessToken(tokenResponse.accessToken)

                // 如果响应中包含新的刷新令牌，也要保存
                tokenResponse.refreshToken?.let { newRefreshToken ->
                    if (newRefreshToken.isNotEmpty()) {
                        Log.d(TAG, "💾 保存新的刷新令牌: ${newRefreshToken.take(10)}...")
                        userPreferences.saveRefreshToken(newRefreshToken)
                    }
                }

                // 计算并保存令牌过期时间（与iOS版本一致）
                tokenResponse.expiresIn?.let { expiresIn ->
                    val expiresAt = System.currentTimeMillis() + (expiresIn * 1000)
                    Log.d(TAG, "⏱️ 保存令牌过期时间: ${Date(expiresAt)}")
                    userPreferences.saveTokenExpiresAt(expiresAt)
                }

                Log.i(TAG, "✅ 令牌刷新成功")
                tokenResponse.accessToken
            } catch (e: Exception) {
                Log.e(TAG, "❌ 令牌刷新失败: ${e.message}", e)
                // 刷新失败，清除认证信息
                clearAuthCredentials()
                throw e
            } finally {
                refreshTask = null
            }
        }
        
        refreshTask = task
        task.await()
    }

    /**
     * 执行令牌刷新请求，支持重试机制
     * 与iOS版本的refreshTokenRequest方法类似
     */
    private suspend fun performRefreshRequestWithRetry(request: Request, retryCount: Int): okhttp3.Response {
        try {
            Log.d(TAG, "🔄 执行令牌刷新请求（剩余重试次数: $retryCount）")
            return okHttpClient.newCall(request).execute()
        } catch (e: Exception) {
            if (retryCount > 0) {
                Log.w(TAG, "⚠️ 令牌刷新请求失败，准备重试: ${e.message}")

                // 指数退避延迟策略
                val delay = Math.pow(2.0, (3 - retryCount).toDouble()).toLong() // 2秒、4秒
                Log.d(TAG, "⏱️ 等待 $delay 秒后重试...")

                kotlinx.coroutines.delay(delay * 1000)

                // 递归重试
                return performRefreshRequestWithRetry(request, retryCount - 1)
            } else {
                Log.e(TAG, "❌ 所有重试都失败了: ${e.message}")
                throw e
            }
        }
    }

    /**
     * 处理OAuth回调，获取后端user_id
     */
    private suspend fun handleOAuthCallback(code: String, state: String): NetworkResult<OAuthCallbackResponse> {
        return safeApiCall {
            apiService.handleOAuthCallback(code, state)
        }
    }



    /**
     * 获取用户资料
     */
    private suspend fun getUserProfile(): NetworkResult<UserInfoResponse> {
        return safeApiCall {
            apiService.getUserProfile()
        }
    }
    
    /**
     * 加载用户资料
     */
    private suspend fun loadUserProfile() {
        when (val result = getUserProfile()) {
            is NetworkResult.Success -> {
                val userInfo = result.data
                // 将UserInfoResponse转换为User对象
                val user = User(
                    userId = userInfo.userId,
                    username = userInfo.username,
                    email = userInfo.email,
                    phone = "",
                    nickname = userInfo.nickname ?: "",
                    status = 1,
                    createdAt = "",
                    updatedAt = ""
                )
                updateAuthState(user = user)
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "加载用户资料失败", result.exception)
            }
            else -> {}
        }
    }
    
    /**
     * 登出
     */
    suspend fun logout() {
        Log.i(TAG, "🚪 用户登出")
        clearAuthCredentials()
        updateAuthState(isAuthenticated = false, user = null)
    }
    
    /**
     * 清除认证凭据
     */
    private suspend fun clearAuthCredentials() {
        userPreferences.clearAuthCredentials()
    }
    
    /**
     * 更新认证状态
     */
    private fun updateAuthState(
        isAuthenticated: Boolean? = null,
        isLoading: Boolean? = null,
        error: String? = null,
        user: User? = null
    ) {
        _authState.value = _authState.value.copy(
            isAuthenticated = isAuthenticated ?: _authState.value.isAuthenticated,
            isLoading = isLoading ?: _authState.value.isLoading,
            error = error,
            user = user ?: _authState.value.user
        )
    }
    
    /**
     * 刷新认证状态
     */
    suspend fun refreshAuthState() {
        val isAuthenticated = userPreferences.isAuthenticated().first()
        updateAuthState(isAuthenticated = isAuthenticated)
        if (isAuthenticated) {
            loadUserProfile()
        }
    }

    /**
     * 检查应用启动时的令牌状态
     * 用于应用启动时快速检查令牌是否有效
     */
    suspend fun checkStartupTokenStatus(): Boolean {
        return try {
            val isAuthenticated = userPreferences.isAuthenticated().first()
            val accessToken = userPreferences.getAccessToken().first()

            if (isAuthenticated && !accessToken.isNullOrEmpty()) {
                // 检查令牌是否有效
                if (isTokenValid(accessToken)) {
                    Log.i(TAG, "🚀 启动检查：令牌有效，用户已登录")
                    return true
                } else {
                    Log.w(TAG, "🚀 启动检查：令牌无效或过期")
                    return false
                }
            } else {
                Log.i(TAG, "🚀 启动检查：用户未登录")
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "🚀 启动检查失败: ${e.message}")
            false
        }
    }
    
    // MARK: - Helper Methods
    
    private fun generateCodeVerifier(): String {
        val bytes = ByteArray(32)
        java.security.SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
    
    private fun generateCodeChallenge(codeVerifier: String): String {
        val digest = java.security.MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(codeVerifier.toByteArray())
        return Base64.getUrlEncoder().withoutPadding().encodeToString(hash)
    }

    /**
     * 原生Logto登录方法
     */
    suspend fun signInNative(activity: android.app.Activity): Result<Unit> {
        return try {
            Log.i(TAG, "🚀 开始原生Logto登录")
            updateAuthState(isLoading = true, error = null)

            // 验证配置
            if (!com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()) {
                val error = "Logto配置验证失败"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                return Result.failure(Exception(error))
            }

            Log.i(TAG, "📱 调用Logto SDK登录")
            logtoManager.signIn(activity)

            Log.i(TAG, "🔑 获取访问令牌")
            // 登录成功后获取令牌并保存
            val accessToken = logtoManager.getAccessToken()
            val idToken = logtoManager.getIdToken()
            val refreshToken = logtoManager.getRefreshToken()

            Log.d(TAG, "访问令牌: ${if (accessToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "ID令牌: ${if (idToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "刷新令牌: ${if (refreshToken != null) "已获取" else "未获取"}")

            if (accessToken != null) {
                Log.i(TAG, "💾 保存令牌到本地存储")
                // 保存令牌到本地存储
                userPreferences.saveAccessToken(accessToken)
                idToken?.let { userPreferences.saveIdToken(it) }
                refreshToken?.let { userPreferences.saveRefreshToken(it) }

                Log.i(TAG, "👤 获取Logto用户信息")
                // 获取Logto用户信息
                val userInfo = logtoManager.getUserInfo()
                userInfo?.let { info ->
                    Log.d(TAG, "Logto用户信息: $info")
                    val logtoId = info["sub"] as? String // Logto认证服务的用户ID
                    val email = info["email"] as? String
                    val name = info["name"] as? String

                    logtoId?.let {
                        userPreferences.saveLogtoId(it)
                        Log.i(TAG, "✅ 保存Logto ID: $it")

                        // 🔑 关键修复：调用后端API获取user_id
                        Log.i(TAG, "🔄 调用后端用户信息API获取user_id...")
                        try {
                            // 调用 /api/user/info 获取用户信息，包含正确的user_id
                            val userInfoResult = getUserProfile()
                            when (userInfoResult) {
                                is NetworkResult.Success -> {
                                    val userInfo = userInfoResult.data
                                    Log.i(TAG, "✅ 获取到后端user_id: ${userInfo.userId}")

                                    // 保存正确的user_id和用户信息
                                    userPreferences.saveUserInfo(userInfo.userId, userInfo.email, userInfo.nickname ?: userInfo.username)
                                }
                                is NetworkResult.Error -> {
                                    Log.e(TAG, "❌ 获取用户信息失败: ${userInfoResult.exception.message}")
                                    // 如果获取用户信息失败，我们暂时使用logto_id作为fallback
                                    userPreferences.saveUserId(logtoId)
                                    userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")
                                }
                                else -> {
                                    Log.w(TAG, "⚠️ 获取用户信息返回未知结果")
                                    userPreferences.saveUserId(logtoId)
                                    userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "❌ 获取用户信息异常: ${e.message}")
                            // 异常情况下使用logto_id作为fallback
                            userPreferences.saveUserId(logtoId)
                            userPreferences.saveUserInfo(logtoId, email ?: "", name ?: "")
                        }
                    }
                }

                updateAuthState(isAuthenticated = true, isLoading = false)
                Log.i(TAG, "✅ 原生登录成功")
                Result.success(Unit)
            } else {
                val error = "无法获取访问令牌"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                Result.failure(Exception(error))
            }
        } catch (e: Exception) {
            val errorMsg = "原生登录失败: ${e.message}"
            Log.e(TAG, "❌ $errorMsg", e)
            updateAuthState(isAuthenticated = false, isLoading = false, error = e.message)
            Result.failure(e)
        }
    }

    /**
     * 登出方法
     */
    suspend fun signOut(): Result<Unit> {
        return try {
            updateAuthState(isLoading = true)

            // 使用Logto SDK登出
            logtoManager.signOut()

            // 清除本地存储的认证信息
            clearAuthCredentials()

            Log.i(TAG, "✅ 登出成功")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 登出失败", e)
            // 即使登出失败，也清除本地认证信息
            clearAuthCredentials()
            Result.failure(e)
        }
    }


}

/**
 * 令牌响应模型
 */
@kotlinx.serialization.Serializable
data class TokenResponse(
    @kotlinx.serialization.SerialName("access_token")
    val accessToken: String,
    @kotlinx.serialization.SerialName("refresh_token")
    val refreshToken: String? = null,
    @kotlinx.serialization.SerialName("token_type")
    val tokenType: String = "Bearer",
    @kotlinx.serialization.SerialName("expires_in")
    val expiresIn: Long? = null
)
