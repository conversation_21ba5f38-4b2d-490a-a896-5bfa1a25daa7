# CabyCare Logto 登录机制 API 详细文档

## 概述

本文档详细描述了 CabyCare 系统中基于 Logto 的用户认证和令牌管理机制，包括所有相关 API 的参数、返回值格式和使用流程。

## 基础信息

- **API 基础URL**: `https://api.caby.care/api`
- **认证方式**: Bearer <PERSON> (JWT)
- **数据格式**: JSON
- **编码**: UTF-8
- **Logto 端点**: `https://brx8db.logto.app`

## 认证流程概述

1. **OAuth 授权**: 用户通过 Logto 进行 OAuth 2.0 + PKCE 认证
2. **回调处理**: 系统处理 Logto 回调并获取令牌
3. **令牌使用**: 使用访问令牌调用受保护的 API
4. **令牌刷新**: 使用刷新令牌获取新的访问令牌

---

## API 详细说明

### 1. OAuth 回调处理

**接口**: `GET /api/callback`

**描述**: 处理 Logto OAuth 认证回调，获取用户令牌和信息

#### 请求参数 (Query Parameters)

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `code` | string | 是 | OAuth 授权码 |
| `state` | string | 是 | OAuth 状态参数，用于防止 CSRF 攻击 |
| `code_verifier` | string | 否 | PKCE 验证参数（移动端使用） |

#### 成功响应 (200 OK)

```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "expires_in": 3600,
  "token_type": "Bearer",
  "user_id": "usr_1234567890abcdef",
  "logto_id": "logto_user_1234567890",
  "message": "Login successful"
}
```

#### 字段说明

- `access_token`: JWT 访问令牌，用于 API 调用认证
- `refresh_token`: 刷新令牌，用于获取新的访问令牌
- `id_token`: OpenID Connect ID 令牌，包含用户身份信息
- `expires_in`: 访问令牌过期时间（秒）
- `token_type`: 令牌类型，固定为 "Bearer"
- `user_id`: 系统内部用户 ID (UUID 格式)
- `logto_id`: Logto 用户 ID
- `message`: 操作结果消息

#### 错误响应

**400 Bad Request** - 缺少必要参数
```json
{
  "error": "missing state"
}
```

**401 Unauthorized** - ID Token 验证失败
```json
{
  "error": "invalid id_token"
}
```

**500 Internal Server Error** - 服务器内部错误
```json
{
  "error": "failed to get access token"
}
```

---

### 2. 刷新访问令牌

**接口**: `POST /api/refresh`

**描述**: 使用刷新令牌获取新的访问令牌

#### 请求头

```
Content-Type: application/json
```

#### 请求体

```json
{
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ..."
}
```

#### 请求参数说明

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `refresh_token` | string | 是 | 有效的刷新令牌 |

#### 成功响应 (200 OK)

**完全成功情况**（包含用户信息）:
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "expires_in": 3600,
  "token_type": "Bearer",
  "user_id": "usr_1234567890abcdef",
  "logto_id": "logto_user_1234567890",
  "message": "Token refreshed successfully"
}
```

**部分成功情况**（仅令牌信息）:
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...",
  "expires_in": 3600,
  "token_type": "Bearer",
  "message": "Token refreshed successfully (user info unavailable)"
}
```

#### 错误响应

**400 Bad Request** - 缺少刷新令牌
```json
{
  "error": "missing refresh_token"
}
```

**401 Unauthorized** - 刷新令牌无效或过期
```json
{
  "error": "failed to refresh token",
  "detail": "invalid_grant: refresh token is invalid or expired"
}
```

---

### 3. 获取用户信息

**接口**: `GET /api/user/info`

**描述**: 获取当前认证用户的详细信息

#### 请求头

```
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjEyMzQ1Njc4OTAifQ...
```

#### 成功响应 (200 OK)

```json
{
  "message": "valid token",
  "user_id": "usr_1234567890abcdef",
  "logto_id": "logto_user_1234567890",
  "username": "<EMAIL>",
  "email": "<EMAIL>",
  "nickname": "John Doe",
  "logto_user_info": {
    "sub": "logto_user_1234567890",
    "name": "John Doe",
    "email": "<EMAIL>",
    "picture": "https://lh3.googleusercontent.com/a/default-user=s96-c",
    "email_verified": true,
    "locale": "en",
    "updated_at": 1640995200000
  }
}
```

#### 字段说明

- `user_id`: 系统内部用户 ID
- `logto_id`: Logto 用户 ID
- `username`: 用户名（通常为邮箱）
- `email`: 用户邮箱
- `nickname`: 用户昵称
- `logto_user_info`: Logto 原始用户信息
  - `sub`: Logto 用户唯一标识
  - `name`: 用户显示名称
  - `email`: 邮箱地址
  - `picture`: 用户头像 URL
  - `email_verified`: 邮箱是否已验证
  - `locale`: 用户语言设置
  - `updated_at`: 最后更新时间（Unix 时间戳，毫秒）

#### 错误响应

**401 Unauthorized** - 缺少或无效的令牌
```json
{
  "error": "missing token"
}
```

```json
{
  "error": "invalid token",
  "detail": "token verification failed"
}
```

**500 Internal Server Error** - 服务器内部错误
```json
{
  "error": "failed to get user info",
  "detail": "user service unavailable"
}
```

---

## 数据结构定义

### TokenResponse 结构

```go
type TokenResponse struct {
    AccessToken  string `json:"access_token"`
    TokenType    string `json:"token_type"`
    ExpiresIn    int    `json:"expires_in"`
    RefreshToken string `json:"refresh_token"`
    IdToken      string `json:"id_token"`
}
```

### LogtoUserInfo 结构

```go
type LogtoUserInfo struct {
    Sub           string `json:"sub"`           // Logto 用户 ID
    Name          string `json:"name"`          // 显示名称
    Email         string `json:"email"`         // 邮箱
    Picture       string `json:"picture"`       // 头像 URL
    EmailVerified bool   `json:"email_verified"` // 邮箱验证状态
    Locale        string `json:"locale"`        // 语言设置
    UpdatedAt     int64  `json:"updated_at"`    // 更新时间戳（毫秒）
}
```

### User 结构

```go
type User struct {
    UserID    string    `json:"user_id"`    // 系统用户 ID
    LogtoID   string    `json:"logto_id"`   // Logto 用户 ID
    Username  string    `json:"username"`   // 用户名
    Email     string    `json:"email"`      // 邮箱
    Nickname  string    `json:"nickname"`   // 昵称
    Status    int8      `json:"status"`     // 状态 (1-正常, 2-禁用)
    CreatedAt time.Time `json:"created_at"` // 创建时间
    UpdatedAt time.Time `json:"updated_at"` // 更新时间
}
```

---

## 使用示例

### 1. 完整登录流程示例

```bash
# 1. 构建授权 URL（前端处理）
AUTH_URL="https://brx8db.logto.app/oidc/auth?client_id=m5e89cx7qhnv16ofchw9u&redirect_uri=https://api.caby.care/api/callback&response_type=code&scope=openid+profile+email+offline_access&state=random_state_string"

# 2. 用户完成授权后，系统自动调用回调接口
# GET /api/callback?code=AUTH_CODE&state=random_state_string

# 3. 获取到令牌后，使用访问令牌调用 API
curl -H "Authorization: Bearer ACCESS_TOKEN" \
     https://api.caby.care/api/user/info
```

### 2. 令牌刷新示例

```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{"refresh_token":"REFRESH_TOKEN"}' \
     https://api.caby.care/api/refresh
```

---

## 重要说明

1. **令牌安全**: 访问令牌应安全存储，避免在 URL 或日志中暴露
2. **令牌过期**: 访问令牌默认有效期为 3600 秒（1小时）
3. **刷新机制**: 当访问令牌过期时，使用刷新令牌获取新的访问令牌
4. **PKCE 支持**: 系统支持 PKCE 流程，适用于移动应用等公共客户端
5. **用户创建**: 首次登录时系统会自动创建用户记录
6. **错误处理**: 所有 API 都提供详细的错误信息，便于调试和处理

## 故障排除

1. **回调失败**: 检查 `state` 参数是否正确，`code` 是否有效
2. **令牌刷新失败**: 检查刷新令牌是否过期或无效
3. **用户信息获取失败**: 检查访问令牌是否有效，是否包含正确的 Bearer 前缀
4. **CORS 问题**: 确保前端域名已在 Logto 中正确配置

此文档提供了完整的 Logto 登录机制实现细节，可用于指导 AI 或开发人员完成相关功能的开发和调试。
