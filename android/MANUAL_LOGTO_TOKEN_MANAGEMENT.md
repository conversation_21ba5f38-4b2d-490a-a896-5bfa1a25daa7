# 手动Logto令牌管理实现总结

## 🎯 实现目标

根据后端API文档，实现手动管理Logto刷新令牌，而不是使用Logto原生SDK管理刷新令牌，确保与iOS版本的一致性。

## 📋 后端API分析

根据`logto_login_api_documentation.md`文档：

### 1. 回调端点 `/api/callback`
- **用途**: 处理OAuth回调，返回完整的令牌信息
- **参数**: `code`, `state`
- **响应**: 包含`access_token`, `refresh_token`, `id_token`, `expires_in`, `user_id`等

### 2. 刷新端点 `/api/refresh`
- **用途**: 使用refresh_token获取新的access_token
- **参数**: `refresh_token`
- **响应**: 包含新的`access_token`, `refresh_token`, `expires_in`等

## 🔧 Android实现方案

### 1. 数据模型更新

#### A. 更新OAuthCallbackResponse
```kotlin
@Serializable
data class OAuthCallbackResponse(
    @SerialName("access_token") val accessToken: String,
    @SerialName("refresh_token") val refreshToken: String? = null,
    @SerialName("id_token") val idToken: String? = null,
    @SerialName("expires_in") val expiresIn: Long? = null,
    @SerialName("token_type") val tokenType: String = "Bearer",
    @SerialName("user_id") val userId: String,
    @SerialName("logto_id") val logtoId: String? = null,
    val message: String,
    // 可选的用户信息字段
    val username: String? = null,
    val email: String? = null,
    val nickname: String? = null
)
```

#### B. 新增TokenRefreshResponse
```kotlin
@Serializable
data class TokenRefreshResponse(
    @SerialName("access_token") val accessToken: String,
    @SerialName("refresh_token") val refreshToken: String? = null,
    @SerialName("id_token") val idToken: String? = null,
    @SerialName("expires_in") val expiresIn: Long? = null,
    @SerialName("token_type") val tokenType: String = "Bearer",
    val message: String,
    // 可选的用户信息字段
    @SerialName("user_id") val userId: String? = null,
    @SerialName("logto_id") val logtoId: String? = null
)
```

### 2. 登录流程优化

#### A. 移除直接OIDC调用
- **删除**: `exchangeCodeForTokens`方法
- **原因**: 不再直接调用Logto的OIDC端点

#### B. 使用后端callback API
```kotlin
// 🔑 使用后端callback API获取完整的令牌信息（包括refresh_token）
Log.i(TAG, "🔄 调用后端callback API获取完整令牌信息...")
val callbackResult = handleOAuthCallback(code, state)
```

#### C. 保存完整令牌信息
```kotlin
// 保存所有令牌信息
userPreferences.saveAccessToken(callbackResponse.accessToken)
callbackResponse.refreshToken?.let { 
    Log.i(TAG, "💾 保存刷新令牌: ${it.take(20)}...")
    userPreferences.saveRefreshToken(it) 
}
callbackResponse.idToken?.let { userPreferences.saveIdToken(it) }

// 计算并保存令牌过期时间
callbackResponse.expiresIn?.let { expiresIn ->
    val expiresAt = System.currentTimeMillis() + (expiresIn * 1000)
    Log.i(TAG, "⏱️ 保存令牌过期时间: ${Date(expiresAt)}")
    userPreferences.saveTokenExpiresAt(expiresAt)
}
```

### 3. 令牌刷新机制

#### A. 使用后端刷新端点
```kotlin
// 🔑 使用与iOS版本相同的后端自定义刷新端点和请求格式
val requestBody = mapOf(
    "refresh_token" to refreshToken  // 只发送refresh_token，与iOS版本一致
)

val request = Request.Builder()
    .url(LogtoConfig.REFRESH_URL)  // https://api.caby.care/api/refresh
    .post(body)
    .addHeader("Content-Type", "application/json")
    .addHeader("Accept", "application/json")
    .addHeader("X-Client-Platform", "Android")
    .build()
```

#### B. 处理刷新响应
```kotlin
val refreshResponse = Json.decodeFromString(TokenRefreshResponse.serializer(), responseBody)

// 验证响应是否成功
if (!refreshResponse.isSuccess) {
    Log.e(TAG, "❌ 令牌刷新失败: ${refreshResponse.message}")
    throw Exception("Token refresh failed: ${refreshResponse.message}")
}

// 保存新令牌
userPreferences.saveAccessToken(refreshResponse.accessToken)
refreshResponse.refreshToken?.let { newRefreshToken ->
    if (newRefreshToken.isNotEmpty()) {
        Log.i(TAG, "💾 保存新的刷新令牌")
        userPreferences.saveRefreshToken(newRefreshToken)
    }
}
```

### 4. 令牌验证改进

#### A. 检测无效令牌格式
```kotlin
// 检查令牌格式和有效性
if (accessToken.length < 100) {
    Log.w(TAG, "⚠️ 访问令牌长度异常（${accessToken.length}），可能不是标准JWT格式")
    Log.w(TAG, "⚠️ 这可能是Logto SDK的内部令牌，需要重新登录获取OIDC令牌")
    clearAuthCredentials()
    updateAuthState(isAuthenticated = false, isLoading = false)
}
```

#### B. 详细的令牌验证日志
```kotlin
private fun isTokenValid(token: String): Boolean {
    Log.i(TAG, "🔍 开始检查令牌有效性...")
    
    // JWT格式验证
    val parts = token.split(".")
    if (parts.size != 3) {
        Log.e(TAG, "❌ 令牌格式无效: 不是有效的JWT格式（应有3部分）")
        return false
    }
    
    // 过期时间检查
    val payload = String(Base64.getUrlDecoder().decode(parts[1]))
    val json = Json.parseToJsonElement(payload).jsonObject
    val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull()
    
    // 详细日志输出
    Log.i(TAG, "🔍 令牌有效性检查结果:")
    Log.i(TAG, "  - 过期时间: ${Date(expirationTime)}")
    Log.i(TAG, "  - 当前时间: ${Date(currentTime)}")
    Log.i(TAG, "  - 是否有效: $isValid")
    Log.i(TAG, "  - 剩余时间: ${(expirationTime - currentTime) / 1000 / 60} 分钟")
}
```

## 🎯 与iOS版本的一致性

### 1. API端点一致性
- **回调端点**: `https://api.caby.care/api/callback`
- **刷新端点**: `https://api.caby.care/api/refresh`
- **请求格式**: 与iOS版本完全一致

### 2. 令牌管理一致性
- **存储机制**: 存储access_token, refresh_token, id_token
- **过期时间**: 计算并存储令牌过期时间
- **刷新策略**: 过期前10分钟自动刷新

### 3. 错误处理一致性
- **重试机制**: 3次指数退避重试
- **失败处理**: 刷新失败时清除认证信息
- **日志格式**: 与iOS版本相似的日志输出

## 📊 技术优势

### 1. 完全手动控制
- **不依赖SDK**: 完全脱离Logto SDK的令牌管理
- **自定义逻辑**: 可以根据需要自定义令牌处理逻辑
- **调试友好**: 详细的日志输出，便于调试

### 2. 与后端紧密集成
- **统一API**: 使用后端提供的统一API
- **完整信息**: 获取完整的用户和令牌信息
- **一致性**: 与iOS版本保持完全一致

### 3. 安全性提升
- **标准JWT**: 使用标准的JWT令牌格式
- **安全存储**: 使用DataStore安全存储令牌
- **自动清理**: 失败时自动清理无效令牌

## ✅ 实现状态

- **编译状态**: ✅ 成功
- **数据模型**: ✅ 完成
- **登录流程**: ✅ 完成
- **令牌刷新**: ✅ 完成
- **错误处理**: ✅ 完成

## 🎉 预期效果

1. **完整令牌管理**: 获取并正确存储所有令牌信息
2. **自动刷新**: 令牌过期前自动刷新，用户无感知
3. **错误恢复**: 刷新失败时自动重试和错误恢复
4. **一致体验**: 与iOS版本完全一致的令牌管理体验

现在Android应用具备了完全手动的Logto令牌管理能力，不再依赖Logto SDK，与iOS版本保持完全一致！
